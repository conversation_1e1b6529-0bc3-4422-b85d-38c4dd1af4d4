/*
=======================================================
= File: gmx_imap_client.dart
= Project: LavaMail
= Description:
=   - GMX IMAP client for email operations
=   - Handles email fetching, folder management, and message operations
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../auth/gmx_auth_service.dart';

// Helper class for batch ranges
class _BatchRange {
  final int start;
  final int end;
  _BatchRange(this.start, this.end);
}

// =======================================================
// = Class: GmxImapClient
// = Description: IMAP client wrapper for GMX email operations
// =======================================================
class GmxImapClient {
  final Logger _logger = Logger();
  ImapClient? _client;
  bool _isConnected = false;
  
  // Cache for mailbox information
  final Map<String, Mailbox> _mailboxCache = {};
  final Map<String, DateTime> _mailboxCacheTimestamp = {};
  static const Duration _mailboxCacheDuration = Duration(minutes: 2);
  
  // Connection pool for parallel operations
  static final Map<String, ImapClient> _connectionPool = {};
  static const int _maxPoolSize = 3;
  
  // Batch size for message fetching
  static const int _defaultBatchSize = 50;

  // =======================================================
  // = Function: initialize
  // = Description: Initializes the IMAP client with user credentials
  // =======================================================
  Future<void> initialize(dynamic user) async {
    // Store user for connection purposes
    // The actual connection will be established when needed
    _logger.d('GmxImapClient initialized for user: ${user?.email}');
  }

  // =======================================================
  // = Function: connect
  // = Description: Establishes connection to GMX IMAP server with connection pooling
  // =======================================================
  Future<void> connect() async {
    try {
      if (_isConnected && _client != null) {
        return; // Already connected
      }

      _logger.d('Connecting to GMX IMAP server');
      
      // Try to reuse an existing connection from the pool
      final availableConnection = _getAvailableConnection();
      if (availableConnection != null) {
        _client = availableConnection;
        _isConnected = true;
        _logger.i('Reused existing GMX IMAP connection from pool');
        return;
      }

      // Create new connection if pool not full
      if (_connectionPool.length < _maxPoolSize) {
        _client = await GmxAuthService.getImapClient();
        _connectionPool[_client.hashCode.toString()] = _client!;
        _isConnected = true;
        _logger.i('Created new GMX IMAP connection (pool size: ${_connectionPool.length})');
      } else {
        // Wait for an available connection if pool is full
        _logger.w('Connection pool full, waiting for available connection...');
        await Future.delayed(const Duration(milliseconds: 500));
        return await connect();
      }
    } catch (e, stack) {
      _logger.e('Failed to connect to GMX IMAP server', error: e, stackTrace: stack);
      _isConnected = false;
      rethrow;
    }
  }

  // Get available connection from pool
  ImapClient? _getAvailableConnection() {
    for (final connection in _connectionPool.values) {
      if (!connection.isConnected) {
        _connectionPool.remove(connection.hashCode.toString());
        continue;
      }
      return connection;
    }
    return null;
  }

  // =======================================================
  // = Function: disconnect
  // = Description: Disconnects from IMAP server
  // =======================================================
  Future<void> disconnect() async {
    try {
      if (_client != null && _isConnected) {
        await _client!.disconnect();
        _logger.i('Disconnected from GMX IMAP server');
      }
    } catch (e) {
      _logger.w('Error disconnecting from IMAP server: $e');
    } finally {
      _client = null;
      _isConnected = false;
    }
  }

  // =======================================================
  // = Function: _ensureConnected
  // = Description: Ensures IMAP connection is established
  // =======================================================
  Future<void> _ensureConnected() async {
    if (!_isConnected || _client == null) {
      await connect();
    }
  }

  // =======================================================
  // = Function: fetchMessages
  // = Description: Fetches messages with optimized batching
  // =======================================================
  Future<List<MimeMessage>> fetchMessages({
    String mailboxName = 'INBOX',
    int? limit,
    bool fetchStructure = false,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Fetching messages from $mailboxName (limit: $limit)');

      final mailbox = await _selectMailboxWithCache(mailboxName);
      if (mailbox == null) {
        _logger.w('Mailbox $mailboxName not found or not accessible');
        return [];
      }

      final messageCount = mailbox.messagesExists;
      if (messageCount == 0) {
        _logger.d('No messages found in $mailboxName');
        return [];
      }

      // Calculate optimal batch size and ranges
      final fetchLimit = limit ?? messageCount;
      final startIndex = messageCount > fetchLimit ? messageCount - fetchLimit + 1 : 1;
      final endIndex = messageCount;
      final batchSize = _defaultBatchSize;
      final batches = _calculateBatches(startIndex, endIndex, batchSize);

      // Fetch messages in parallel batches
      final futures = <Future<List<MimeMessage>>>[];
      for (final batch in batches) {
        futures.add(_fetchMessageBatch(
          batch.start,
          batch.end,
          fetchStructure,
        ));
      }

      // Wait for all batches and combine results
      final results = await Future.wait(futures);
      final messages = results.expand((batch) => batch).toList();
      _logger.i('Fetched ${messages.length} messages from $mailboxName in ${batches.length} batches');
      
      return messages;
    } catch (e, stack) {
      _logger.e('Failed to fetch messages from $mailboxName', error: e, stackTrace: stack);
      return [];
    }
  }

  // Calculate optimal batch ranges
  List<_BatchRange> _calculateBatches(int start, int end, int batchSize) {
    final batches = <_BatchRange>[];
    for (var i = start; i <= end; i += batchSize) {
      final batchEnd = (i + batchSize - 1) > end ? end : (i + batchSize - 1);
      batches.add(_BatchRange(i, batchEnd));
    }
    return batches;
  }

  // Fetch a single batch of messages
  Future<List<MimeMessage>> _fetchMessageBatch(
    int start,
    int end,
    bool fetchStructure,
  ) async {
    try {
      final sequence = MessageSequence.fromRange(start, end);
      final fetchResult = await _client!.fetchMessages(
        sequence,
        fetchStructure ? 'BODY.PEEK[]' : 'ENVELOPE',
      );
      return fetchResult.messages;
    } catch (e) {
      _logger.w('Error fetching batch $start-$end: $e');
      return [];
    }
  }

  // =======================================================
  // = Function: _selectMailboxWithCache
  // = Description: Select mailbox with caching
  // =======================================================
  Future<Mailbox?> _selectMailboxWithCache(String mailboxName) async {
    // Check cache first
    if (_mailboxCache.containsKey(mailboxName)) {
      final cacheTimestamp = _mailboxCacheTimestamp[mailboxName];
      if (cacheTimestamp != null && 
          DateTime.now().difference(cacheTimestamp) < _mailboxCacheDuration) {
        return _mailboxCache[mailboxName];
      }
    }

    // Cache miss or expired, fetch fresh data
    final mailbox = await selectMailbox(mailboxName);
    if (mailbox != null) {
      _mailboxCache[mailboxName] = mailbox;
      _mailboxCacheTimestamp[mailboxName] = DateTime.now();
    }
    return mailbox;
  }

  // =======================================================
  // = Function: searchMessages
  // = Description: Searches for messages matching criteria
  // =======================================================
  Future<List<MimeMessage>> searchMessages({
    String mailboxName = 'INBOX',
    String? query,
    int? limit,
  }) async {
    try {
      await _ensureConnected();
      _logger.d('Searching messages in $mailboxName with query: $query');

      await _client!.selectInbox();

      SearchImapResult searchResult;
      if (query != null && query.isNotEmpty) {
        // Simple text search using SEARCH command
        searchResult = await _client!.searchMessages(searchCriteria: 'TEXT "$query"');
      } else {
        // Search all messages
        searchResult = await _client!.searchMessages(searchCriteria: 'ALL');
      }

      if (searchResult.matchingSequence?.isEmpty ?? true) {
        _logger.d('No messages found matching search criteria');
        return [];
      }

      // Fetch the matching messages
      final sequence = searchResult.matchingSequence!;
      final fetchLimit = limit != null ? 
        MessageSequence.fromIds(sequence.toList().take(limit).toList()) : 
        sequence;

      final fetchResult = await _client!.fetchMessages(
        fetchLimit,
        'BODY.PEEK[]',
      );

      List<MimeMessage> messages = fetchResult.messages;

      // Simple local filtering if query is provided
      if (query != null && query.isNotEmpty) {
        final queryLower = query.toLowerCase();
        messages = messages.where((message) {
          final subject = message.decodeSubject()?.toLowerCase() ?? '';
          final from = message.from?.toString().toLowerCase() ?? '';
          return subject.contains(queryLower) || from.contains(queryLower);
        }).toList();
      }

      // Apply limit if specified
      if (limit != null && messages.length > limit) {
        messages = messages.take(limit).toList();
      }

      _logger.i('Found ${messages.length} messages matching search criteria');
      return messages;
    } catch (e) {
      _logger.e('Failed to search messages in $mailboxName', error: e);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxes
  // = Description: Gets list of available mailboxes
  // =======================================================
  Future<List<Mailbox>> getMailboxes() async {
    try {
      await _ensureConnected();
      _logger.d('Fetching mailbox list');

      final listResult = await _client!.listMailboxes();
      final mailboxes = listResult;
      
      _logger.i('Found ${mailboxes.length} mailboxes');
      return mailboxes;
    } catch (e, stack) {
      _logger.e('Failed to fetch mailboxes', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: getMailboxStatus
  // = Description: Gets status information for a mailbox
  // =======================================================
  Future<Mailbox?> getMailboxStatus(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Getting status for mailbox: $mailboxName');

      // Try to select the mailbox to get accurate message counts
      final mailbox = await selectMailbox(mailboxName);

      if (mailbox != null) {
        _logger.d('Mailbox $mailboxName: ${mailbox.messagesExists} messages, ${mailbox.messagesUnseen} unread');
        return mailbox;
      }

      _logger.w('Mailbox $mailboxName not found or could not be selected');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to get mailbox status for $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: selectMailbox
  // = Description: Selects a specific mailbox for operations
  // =======================================================
  Future<Mailbox?> selectMailbox(String mailboxName) async {
    try {
      await _ensureConnected();
      _logger.d('Selecting mailbox: $mailboxName');

      // Handle INBOX specially for compatibility
      if (mailboxName.toUpperCase() == 'INBOX') {
        final mailbox = await _client!.selectInbox();
        _logger.d('Selected mailbox $mailboxName successfully');
        return mailbox;
      }

      // For other mailboxes, find the mailbox object first, then select it
      final mailboxes = await _client!.listMailboxes();
      final targetMailbox = mailboxes.where(
        (mb) => mb.name.toLowerCase() == mailboxName.toLowerCase(),
      ).firstOrNull;

      if (targetMailbox != null) {
        try {
          final selectedMailbox = await _client!.selectMailbox(targetMailbox);
          _logger.d('Selected mailbox ${targetMailbox.name} successfully: ${selectedMailbox.messagesExists} messages');
          return selectedMailbox;
        } catch (e) {
          _logger.w('Failed to select mailbox ${targetMailbox.name}: $e');
          return null;
        }
      }

      _logger.w('Mailbox $mailboxName not found');
      return null;
    } catch (e, stack) {
      _logger.e('Failed to select mailbox $mailboxName', error: e, stackTrace: stack);
      return null;
    }
  }

  // =======================================================
  // = Function: markAsRead
  // = Description: Marks a message as read
  // =======================================================
  Future<bool> markAsRead(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as read: no UID');
        return false;
      }

      await _client!.markSeen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as read');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as read', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: markAsUnread
  // = Description: Marks a message as unread
  // =======================================================
  Future<bool> markAsUnread(MimeMessage message) async {
    try {
      await _ensureConnected();
      
      if (message.uid == null) {
        _logger.w('Cannot mark message as unread: no UID');
        return false;
      }

      await _client!.markUnseen(MessageSequence.fromId(message.uid!), silent: true);
      _logger.d('Marked message ${message.uid} as unread');
      return true;
    } catch (e) {
      _logger.e('Failed to mark message as unread', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: deleteMessage
  // = Description: Deletes a message
  // =======================================================
  Future<bool> deleteMessage(MimeMessage message) async {
    try {
      await _ensureConnected();

      if (message.uid == null) {
        _logger.w('Cannot delete message: no UID');
        return false;
      }

      await _client!.markDeleted(MessageSequence.fromId(message.uid!));
      await _client!.expunge();
      _logger.d('Deleted message ${message.uid}');
      return true;
    } catch (e) {
      _logger.e('Failed to delete message', error: e);
      return false;
    }
  }

  // =======================================================
  // = Function: discoverFolderStructure
  // = Description: Discovers the complete GMX folder structure with detailed analysis
  // = Inspired by the Python gmx.py script for comprehensive folder discovery
  // =======================================================
  Future<List<Map<String, dynamic>>> discoverFolderStructure() async {
    try {
      await _ensureConnected();
      _logger.d('Starting comprehensive GMX folder structure discovery');

      // Step 1: Get all mailboxes using multiple methods for robustness
      final listResult = await _getMailboxesRobust();
      final discoveredFolders = <Map<String, dynamic>>[];

      _logger.i('=== DETAILED GMX FOLDER ANALYSIS ===');
      _logger.i('Total number of folders: ${listResult.length}');

      // Step 2: Analyze each folder in detail
      int totalMessages = 0;
      int totalUnread = 0;
      int selectableFolders = 0;

      for (final mailbox in listResult) {
        try {
          final folderInfo = await _parseDetailedFolderInfo(mailbox);
          discoveredFolders.add(folderInfo);

          // Accumulate totals
          final messageCount = folderInfo['messageCount'] as int? ?? 0;
          final unreadCount = folderInfo['unreadCount'] as int? ?? 0;
          totalMessages += messageCount;
          totalUnread += unreadCount;

          if (folderInfo['isSelectable'] == true) {
            selectableFolders++;
          }

          _logger.d('Name: \'${folderInfo['name']}\'');
          _logger.d('Display Name: \'${folderInfo['displayName']}\'');
          _logger.d('Path: \'${folderInfo['path']}\'');
          _logger.d('Flags: ${folderInfo['flags']}');
          _logger.d('Message Count: ${folderInfo['messageCount']}');
          _logger.d('Unread Count: ${folderInfo['unreadCount']}');
          _logger.d('Is Selectable: ${folderInfo['isSelectable']}');
          _logger.d('-' * 50);
        } catch (e) {
          _logger.w('Error analyzing folder ${mailbox.name}: $e');
          // Add basic info even if detailed analysis fails
          discoveredFolders.add({
            'name': mailbox.name,
            'displayName': _getFolderDisplayName(mailbox.name),
            'path': mailbox.path,
            'flags': <String>[],
            'messageCount': 0,
            'unreadCount': 0,
            'isSelectable': false,
            'hasChildren': mailbox.hasChildren,
            'error': e.toString(),
          });
        }
      }

      // Step 3: Log comprehensive summary
      _logger.i('GMX folder structure discovery completed:');
      _logger.i('- Total folders discovered: ${discoveredFolders.length}');
      _logger.i('- Selectable folders: $selectableFolders');
      _logger.i('- Total messages across all folders: $totalMessages');
      _logger.i('- Total unread messages: $totalUnread');

      // Step 4: Validate discovery results
      _validateDiscoveryResults(discoveredFolders, totalMessages);

      return discoveredFolders;
    } catch (e, stack) {
      _logger.e('Failed to discover GMX folder structure', error: e, stackTrace: stack);
      return [];
    }
  }

  // =======================================================
  // = Function: _getMailboxesRobust
  // = Description: Gets mailboxes using multiple methods for maximum compatibility
  // = Includes GMX-specific discovery patterns
  // =======================================================
  Future<List<Mailbox>> _getMailboxesRobust() async {
    final allMailboxes = <Mailbox>[];
    final seenNames = <String>{};

    // Method 1: Standard listMailboxes
    try {
      _logger.d('Attempting standard mailbox listing...');
      final standardResult = await _client!.listMailboxes();
      for (final mailbox in standardResult) {
        if (!seenNames.contains(mailbox.name)) {
          allMailboxes.add(mailbox);
          seenNames.add(mailbox.name);
        }
      }
      _logger.i('Standard mailbox listing: ${standardResult.length} folders');
    } catch (e) {
      _logger.w('Standard mailbox listing failed: $e');
    }

    // Method 2: List with wildcard pattern (GMX specific)
    try {
      _logger.d('Attempting wildcard pattern mailbox listing...');
      final wildcardResult = await _client!.listMailboxes(path: '*');
      for (final mailbox in wildcardResult) {
        if (!seenNames.contains(mailbox.name)) {
          allMailboxes.add(mailbox);
          seenNames.add(mailbox.name);
        }
      }
      _logger.i('Wildcard pattern listing: ${wildcardResult.length} folders');
    } catch (e) {
      _logger.w('Wildcard pattern listing failed: $e');
    }

    // Method 3: List with percentage pattern (some IMAP servers)
    try {
      _logger.d('Attempting percentage pattern mailbox listing...');
      final percentResult = await _client!.listMailboxes(path: '%');
      for (final mailbox in percentResult) {
        if (!seenNames.contains(mailbox.name)) {
          allMailboxes.add(mailbox);
          seenNames.add(mailbox.name);
        }
      }
      _logger.i('Percentage pattern listing: ${percentResult.length} folders');
    } catch (e) {
      _logger.w('Percentage pattern listing failed: $e');
    }

    // Method 4: Try common GMX folder names explicitly
    final commonGmxFolders = [
      'INBOX', 'Sent', 'Drafts', 'Trash', 'Spam', 'Junk',
      'Gesendet', 'Entwürfe', 'Papierkorb', 'Spamverdacht',
      'Envoyés', 'Brouillons', 'Corbeille', 'Courrier indésirable',
      'Archive', 'Archiv', 'Archives', 'Important', 'Wichtig',
      'Templates', 'Vorlagen', 'Modèles', 'Notes', 'Notizen'
    ];

    for (final folderName in commonGmxFolders) {
      if (!seenNames.contains(folderName)) {
        try {
          final listResult = await _client!.listMailboxes(path: folderName);
          for (final mailbox in listResult) {
            if (!seenNames.contains(mailbox.name)) {
              allMailboxes.add(mailbox);
              seenNames.add(mailbox.name);
            }
          }
        } catch (e) {
          // Ignore errors for individual folder checks
        }
      }
    }

    // Method 5: List with empty path (fallback)
    try {
      _logger.d('Attempting empty path mailbox listing...');
      final emptyResult = await _client!.listMailboxes(path: '');
      for (final mailbox in emptyResult) {
        if (!seenNames.contains(mailbox.name)) {
          allMailboxes.add(mailbox);
          seenNames.add(mailbox.name);
        }
      }
      _logger.i('Empty path listing: ${emptyResult.length} folders');
    } catch (e) {
      _logger.w('Empty path listing failed: $e');
    }

    _logger.i('Total unique mailboxes discovered: ${allMailboxes.length}');

    if (allMailboxes.isEmpty) {
      _logger.e('ERROR: No mailboxes discovered with any method!');
    }

    return allMailboxes;
  }

  // =======================================================
  // = Function: _parseDetailedFolderInfo
  // = Description: Parses detailed information for a single folder
  // = Similar to parse_folder_info in the Python script
  // =======================================================
  Future<Map<String, dynamic>> _parseDetailedFolderInfo(Mailbox mailbox) async {
    try {
      // Get basic folder information
      final folderInfo = {
        'name': mailbox.name,
        'displayName': _getFolderDisplayName(mailbox.name),
        'path': mailbox.path,
        'flags': mailbox.flags.map((flag) => flag.name).toList(),
        'hasChildren': mailbox.hasChildren,
        'isSelectable': true,
        'messageCount': 0,
        'unreadCount': 0,
        'separator': mailbox.pathSeparator,
        'folderType': _determineFolderType(mailbox.name),
        'rawInfo': '${mailbox.name} (${mailbox.path})',
      };

      // Try multiple methods to get message counts
      bool statsObtained = false;

      // Method 1: Try to select the mailbox for accurate counts
      try {
        final selectedMailbox = await selectMailbox(mailbox.name);
        if (selectedMailbox != null) {
          folderInfo['messageCount'] = selectedMailbox.messagesExists;
          folderInfo['unreadCount'] = selectedMailbox.messagesUnseen;
          folderInfo['isSelectable'] = true;
          statsObtained = true;
          _logger.d('Selected ${mailbox.name}: ${selectedMailbox.messagesExists} messages, ${selectedMailbox.messagesUnseen} unread');
        }
      } catch (e) {
        _logger.w('Cannot select folder ${mailbox.name}: $e');
      }

      // Method 2: Try to get basic info from mailbox properties if selection failed
      if (!statsObtained) {
        try {
          // Check if the mailbox has basic message count information
          // Some IMAP servers provide this in the LIST response
          if (mailbox.messagesExists > 0) {
            folderInfo['messageCount'] = mailbox.messagesExists;
            folderInfo['unreadCount'] = mailbox.messagesUnseen;
            folderInfo['isSelectable'] = true;
            statsObtained = true;
            _logger.d('Basic info ${mailbox.name}: ${mailbox.messagesExists} messages, ${mailbox.messagesUnseen} unread');
          }
        } catch (e) {
          _logger.w('Cannot get basic info for ${mailbox.name}: $e');
        }
      }

      // Method 3: Check if folder is selectable based on flags
      if (!statsObtained) {
        final flags = mailbox.flags.map((f) => f.name.toLowerCase()).toList();
        final isNoSelect = flags.contains('\\noselect') || flags.contains('noselect');
        folderInfo['isSelectable'] = !isNoSelect;

        if (isNoSelect) {
          _logger.d('Folder ${mailbox.name} marked as \\NoSelect');
        } else {
          folderInfo['selectError'] = 'Could not retrieve message counts';
        }
      }

      return folderInfo;
    } catch (e) {
      _logger.e('Error parsing folder info for ${mailbox.name}', error: e);
      rethrow;
    }
  }

  // =======================================================
  // = Function: _determineFolderType
  // = Description: Determines the type of folder based on name and flags
  // =======================================================
  String _determineFolderType(String folderName) {
    final lowerName = folderName.toLowerCase();

    if (lowerName == 'inbox') return 'inbox';
    if (_isSentFolder(lowerName)) return 'sent';
    if (_isSpamFolder(lowerName)) return 'spam';
    if (_isTrashFolder(lowerName)) return 'trash';
    if (_isDraftsFolder(lowerName)) return 'drafts';
    if (_isArchiveFolder(lowerName)) return 'archive';

    return 'custom';
  }

  // Helper methods for folder type detection (comprehensive GMX support)
  bool _isSentFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'sent' ||
           lowerName == 'gesendet' ||
           lowerName == 'envoyés' ||
           lowerName == 'sent items' ||
           lowerName == 'gesendete objekte' ||
           lowerName == 'gesendete elemente' ||
           lowerName == 'sent mail' ||
           lowerName == 'outbox' ||
           lowerName == 'sortant' ||
           lowerName == 'boîte d\'envoi' ||
           lowerName == 'ausgang' ||
           lowerName == 'versendete nachrichten' ||
           // GMX specific variations
           lowerName.contains('gesendet') ||
           lowerName.contains('envoy') ||
           lowerName.contains('sent');
  }

  bool _isSpamFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'spam' ||
           lowerName == 'junk' ||
           lowerName == 'spamverdacht' ||
           lowerName == 'junk-e-mail' ||
           lowerName == 'courrier indésirable' ||
           lowerName == 'bulk mail' ||
           lowerName == 'junk mail' ||
           lowerName == 'unwanted' ||
           lowerName == 'spamassassin' ||
           lowerName == 'quarantine' ||
           lowerName == 'quarantäne' ||
           lowerName == 'unerwünscht' ||
           lowerName == 'indésirable' ||
           // GMX specific variations
           lowerName.contains('spam') ||
           lowerName.contains('junk') ||
           lowerName.contains('verdacht');
  }

  bool _isTrashFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'trash' ||
           lowerName == 'deleted' ||
           lowerName == 'papierkorb' ||
           lowerName == 'corbeille' ||
           lowerName == 'deleted items' ||
           lowerName == 'gelöschte objekte' ||
           lowerName == 'gelöschte elemente' ||
           lowerName == 'bin' ||
           lowerName == 'recycle bin' ||
           lowerName == 'wastebasket' ||
           lowerName == 'poubelle' ||
           lowerName == 'mülleimer' ||
           lowerName == 'supprimé' ||
           lowerName == 'supprimés' ||
           // GMX specific variations
           lowerName.contains('papier') ||
           lowerName.contains('gelöscht') ||
           lowerName.contains('trash') ||
           lowerName.contains('deleted');
  }

  bool _isDraftsFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'drafts' ||
           lowerName == 'entwürfe' ||
           lowerName == 'brouillons' ||
           lowerName == 'draft' ||
           lowerName == 'entwurf' ||
           lowerName == 'unsent' ||
           lowerName == 'unsent messages' ||
           lowerName == 'brouillon' ||
           lowerName == 'ébauches' ||
           lowerName == 'vorlagen' ||
           // GMX specific variations
           lowerName.contains('entwurf') ||
           lowerName.contains('brouillon') ||
           lowerName.contains('draft');
  }

  bool _isArchiveFolder(String folderName) {
    final lowerName = folderName.toLowerCase();
    return lowerName == 'archive' ||
           lowerName == 'archiv' ||
           lowerName == 'archives' ||
           lowerName == 'archived' ||
           lowerName == 'archiviert' ||
           lowerName == 'archivé' ||
           lowerName == 'old mail' ||
           lowerName == 'alte nachrichten' ||
           lowerName == 'anciens messages' ||
           // GMX specific variations
           lowerName.contains('archiv') ||
           lowerName.contains('archive') ||
           lowerName.contains('old');
  }

  // =======================================================
  // = Function: _validateDiscoveryResults
  // = Description: Validates the discovery results and logs warnings if needed
  // =======================================================
  void _validateDiscoveryResults(List<Map<String, dynamic>> folders, int totalMessages) {
    // Check if we found essential folders
    final folderNames = folders.map((f) => (f['name'] as String).toLowerCase()).toList();

    bool hasInbox = folderNames.contains('inbox');
    bool hasSent = folders.any((f) => _isSentFolder((f['name'] as String).toLowerCase()));
    bool hasSpam = folders.any((f) => _isSpamFolder((f['name'] as String).toLowerCase()));
    bool hasTrash = folders.any((f) => _isTrashFolder((f['name'] as String).toLowerCase()));
    bool hasDrafts = folders.any((f) => _isDraftsFolder((f['name'] as String).toLowerCase()));

    _logger.i('=== COMPREHENSIVE DISCOVERY VALIDATION ===');
    _logger.i('Essential folders found:');
    _logger.i('- INBOX: ${hasInbox ? "✓" : "✗"}');
    _logger.i('- Sent folder: ${hasSent ? "✓" : "✗"}');
    _logger.i('- Spam folder: ${hasSpam ? "✓" : "✗"}');
    _logger.i('- Trash folder: ${hasTrash ? "✓" : "✗"}');
    _logger.i('- Drafts folder: ${hasDrafts ? "✓" : "✗"}');

    // Check for additional GMX-specific folders
    bool hasArchive = folders.any((f) => _isArchiveFolder((f['name'] as String).toLowerCase()));
    bool hasImportant = folders.any((f) => (f['name'] as String).toLowerCase().contains('important') ||
                                           (f['name'] as String).toLowerCase().contains('wichtig'));
    bool hasTemplates = folders.any((f) => (f['name'] as String).toLowerCase().contains('template') ||
                                           (f['name'] as String).toLowerCase().contains('vorlage'));

    _logger.i('Additional folders found:');
    _logger.i('- Archive folder: ${hasArchive ? "✓" : "✗"}');
    _logger.i('- Important folder: ${hasImportant ? "✓" : "✗"}');
    _logger.i('- Templates folder: ${hasTemplates ? "✓" : "✗"}');

    // Warnings for missing essential folders
    if (!hasInbox) {
      _logger.w('WARNING: INBOX folder not found! This is unusual for GMX.');
    }
    if (!hasSent) {
      _logger.w('WARNING: No Sent folder detected. Check for German/French variants.');
    }
    if (!hasSpam) {
      _logger.w('WARNING: No Spam folder detected. Check for Junk/Spamverdacht variants.');
    }

    if (folders.isEmpty) {
      _logger.e('ERROR: No folders discovered! Check IMAP connection and permissions.');
    } else if (totalMessages == 0) {
      _logger.w('WARNING: No messages found in any folder. Account might be empty or permissions limited.');
    }

    // Log detailed folder analysis
    final folderTypes = <String, int>{};
    final selectableFolders = <String>[];
    final nonSelectableFolders = <String>[];

    for (final folder in folders) {
      final type = folder['folderType'] as String? ?? 'unknown';
      folderTypes[type] = (folderTypes[type] ?? 0) + 1;

      if (folder['isSelectable'] == true) {
        selectableFolders.add(folder['name'] as String);
      } else {
        nonSelectableFolders.add(folder['name'] as String);
      }
    }

    _logger.i('=== DETAILED FOLDER ANALYSIS ===');
    _logger.i('Folder type distribution: $folderTypes');
    _logger.i('Selectable folders (${selectableFolders.length}): ${selectableFolders.take(10).join(", ")}${selectableFolders.length > 10 ? "..." : ""}');
    _logger.i('Non-selectable folders (${nonSelectableFolders.length}): ${nonSelectableFolders.take(5).join(", ")}${nonSelectableFolders.length > 5 ? "..." : ""}');

    // Log folders with messages
    final foldersWithMessages = folders.where((f) => (f['messageCount'] as int? ?? 0) > 0).toList();
    _logger.i('Folders with messages (${foldersWithMessages.length}):');
    for (final folder in foldersWithMessages.take(10)) {
      _logger.i('  - ${folder['name']}: ${folder['messageCount']} messages (${folder['unreadCount']} unread)');
    }

    if (foldersWithMessages.length > 10) {
      _logger.i('  ... and ${foldersWithMessages.length - 10} more folders with messages');
    }
  }

  // =======================================================
  // = Function: _getFolderDisplayName
  // = Description: Maps GMX folder names to user-friendly display names
  // = Handles German, French, English, and other language folder names
  // =======================================================
  String _getFolderDisplayName(String folderName) {
    final lowerName = folderName.toLowerCase();

    // Standard IMAP folders
    if (lowerName == 'inbox') return 'Courrier reçu';

    // Sent folders (various languages and variations)
    if (lowerName == 'sent' ||
        lowerName == 'gesendet' ||
        lowerName == 'envoyés' ||
        lowerName == 'sent items' ||
        lowerName == 'gesendete objekte' ||
        lowerName == 'gesendete elemente' ||
        lowerName == 'sent mail' ||
        lowerName == 'outbox' ||
        lowerName == 'sortant') {
      return 'Envoyés';
    }

    // Spam/Junk folders (comprehensive list)
    if (lowerName == 'spam' ||
        lowerName == 'junk' ||
        lowerName == 'spamverdacht' ||
        lowerName == 'junk-e-mail' ||
        lowerName == 'courrier indésirable' ||
        lowerName == 'bulk mail' ||
        lowerName == 'junk mail' ||
        lowerName == 'unwanted' ||
        lowerName == 'spamassassin' ||
        lowerName == 'quarantine' ||
        lowerName == 'quarantäne') {
      return 'Spam';
    }

    // Trash/Deleted folders (comprehensive list)
    if (lowerName == 'trash' ||
        lowerName == 'deleted' ||
        lowerName == 'papierkorb' ||
        lowerName == 'corbeille' ||
        lowerName == 'deleted items' ||
        lowerName == 'gelöschte objekte' ||
        lowerName == 'gelöschte elemente' ||
        lowerName == 'bin' ||
        lowerName == 'recycle bin' ||
        lowerName == 'wastebasket' ||
        lowerName == 'poubelle') {
      return 'Corbeille';
    }

    // Draft folders (comprehensive list)
    if (lowerName == 'drafts' ||
        lowerName == 'entwürfe' ||
        lowerName == 'brouillons' ||
        lowerName == 'draft' ||
        lowerName == 'entwurf' ||
        lowerName == 'unsent' ||
        lowerName == 'unsent messages') {
      return 'Brouillons';
    }

    // Archive folders (comprehensive list)
    if (lowerName == 'archive' ||
        lowerName == 'archiv' ||
        lowerName == 'archives' ||
        lowerName == 'archived' ||
        lowerName == 'archiviert' ||
        lowerName == 'archivé' ||
        lowerName == 'old mail' ||
        lowerName == 'alte nachrichten') {
      return 'Archives';
    }

    // Important/Priority folders
    if (lowerName == 'important' ||
        lowerName == 'wichtig' ||
        lowerName == 'priority' ||
        lowerName == 'priorité' ||
        lowerName == 'high priority' ||
        lowerName == 'starred' ||
        lowerName == 'favoris' ||
        lowerName == 'favorites') {
      return 'Important';
    }

    // Templates folders
    if (lowerName == 'templates' ||
        lowerName == 'vorlagen' ||
        lowerName == 'modèles' ||
        lowerName == 'template') {
      return 'Modèles';
    }

    // Notes folders
    if (lowerName == 'notes' ||
        lowerName == 'notizen' ||
        lowerName == 'note') {
      return 'Notes';
    }

    // Calendar/Tasks folders
    if (lowerName == 'calendar' ||
        lowerName == 'kalender' ||
        lowerName == 'calendrier' ||
        lowerName == 'tasks' ||
        lowerName == 'aufgaben' ||
        lowerName == 'tâches') {
      return 'Calendrier/Tâches';
    }

    // Return original name if no mapping found, but capitalize first letter
    return folderName.isNotEmpty
        ? '${folderName[0].toUpperCase()}${folderName.substring(1)}'
        : folderName;
  }

  ImapClient getImapClient() {
    if (_client == null) {
      throw Exception('IMAP client not initialized');
    }
    return _client!;
  }
}
