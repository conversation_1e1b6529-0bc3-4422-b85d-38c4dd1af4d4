/*
=======================================================
= File: icloud_online_mode.dart
= Project: LavaMail
= Description:
=   - iCloud API service for online mode using IMAP/SMTP
=   - Implements email operations through direct IMAP/SMTP connection
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:enough_mail/enough_mail.dart';
import '../models/icloud_user.dart';

// =======================================================
// = Class: IcloudApiService
// = Description: iCloud API service using IMAP/SMTP
// =======================================================
class IcloudApiService {
  final IcloudUser? user;
  final Logger _logger = Logger();
  late ImapClient _imapClient;
  bool _initialized = false;

  IcloudApiService(this.user);

  // =======================================================
  // = Function: _ensureInitialized
  // = Description: Ensures the service is initialized
  // =======================================================
  Future<void> _ensureInitialized() async {
    if (!_initialized) {
      if (user == null) {
        throw Exception('No iCloud user provided');
      }
      _imapClient = ImapClient();
      await _imapClient.connectToServer(
        user!.imapHost,
        user!.imapPort,
        isSecure: user!.useSSL,
      );
      await _imapClient.login(user!.email, user!.appPassword);
      _initialized = true;
      _logger.i('iCloud API service initialized for: ${user!.email}');
    }
  }

  // =======================================================
  // = Function: getCategoryStats
  // = Description: Gets statistics for email categories (iCloud uses standard folders)
  // =======================================================
  Future<Map<String, dynamic>> getCategoryStats(String category) async {
    try {
      await _ensureInitialized();
      _logger.d('Getting iCloud stats for category: $category');

      // For now, we'll just use INBOX for all categories
      // TODO: Implement proper mailbox selection based on category
      final mailbox = await _imapClient.selectInbox();

      return {
        'count': mailbox.messagesExists,
        'unread': mailbox.messagesRecent,
        'size': 0, // iCloud doesn't provide size info easily via IMAP
      };
    } catch (e, stack) {
      _logger.e('Error getting iCloud category stats for $category', error: e, stackTrace: stack);
      return {
        'count': 0,
        'unread': 0,
        'size': 0,
      };
    }
  }

  // =======================================================
  // = Function: getEmails
  // = Description: Retrieves emails with optional query parameters and metadata
  // =======================================================
  Future<Map<String, dynamic>> getEmails({
    String? q,
    int? maxResults,
    bool includeMetadata = true,
  }) async {
    try {
      await _ensureInitialized();
      _logger.d('Fetching iCloud emails with query: $q');

      List<MimeMessage> messages;
      if (q != null && q.isNotEmpty) {
        // Search messages using IMAP search
        await _imapClient.selectInbox();
        final searchResult = await _imapClient.searchMessages(searchCriteria: 'TEXT "$q"');

        if (searchResult.matchingSequence?.isEmpty ?? true) {
          messages = [];
        } else {
          final limit = maxResults ?? 50;
          final sequence = searchResult.matchingSequence!;
          final limitedSequence = MessageSequence.fromIds(sequence.toList().take(limit).toList());
          final fetchResult = await _imapClient.fetchMessages(
            limitedSequence,
            includeMetadata ? 'BODY.PEEK[]' : 'ENVELOPE',
          );
          messages = fetchResult.messages;
        }
      } else {
        // Fetch recent messages
        final mailbox = await _imapClient.selectInbox();
        final messageCount = mailbox.messagesExists;

        if (messageCount == 0) {
          messages = [];
        } else {
          final limit = maxResults ?? 50;
          final startIndex = messageCount > limit ? messageCount - limit + 1 : 1;
          final endIndex = messageCount;

          final fetchResult = await _imapClient.fetchMessages(
            MessageSequence.fromRange(startIndex, endIndex),
            includeMetadata ? 'BODY.PEEK[]' : 'ENVELOPE',
          );
          messages = fetchResult.messages;
        }
      }

      final emailList = <Map<String, dynamic>>[];
      for (final message in messages) {
        emailList.add(_convertMessageToMap(message));
      }

      return {
        'messages': emailList,
        'nextPageToken': null, // iCloud doesn't support pagination like Gmail
        'resultSizeEstimate': emailList.length,
      };
    } catch (e, stack) {
      _logger.e('Error fetching iCloud emails', error: e, stackTrace: stack);
      return {
        'messages': <Map<String, dynamic>>[],
        'nextPageToken': null,
        'resultSizeEstimate': 0,
      };
    }
  }

  // =======================================================
  // = Function: _convertMessageToMap
  // = Description: Converts MimeMessage to Map format compatible with Gmail API
  // =======================================================
  Map<String, dynamic> _convertMessageToMap(MimeMessage message) {
    try {
      return {
        'id': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'threadId': message.uid?.toString() ?? message.sequenceId?.toString() ?? 'unknown',
        'labelIds': [],
        'snippet': message.decodeTextPlainPart() ?? '',
        'payload': message.decodeTextHtmlPart() ?? '',
        'sizeEstimate': message.size ?? 0,
        'historyId': message.uid?.toString(),
        'internalDate': message.decodeDate()?.millisecondsSinceEpoch.toString(),
      };
    } catch (e) {
      _logger.w('Error converting message to map: $e');
      return {};
    }
  }

  // =======================================================
  // = Function: getAttachmentAnalysis
  // = Description: Gets attachment analysis (count, emails with attachments, etc.)
  // =======================================================
  Future<Map<String, dynamic>> getAttachmentAnalysis({int maxSample = 50}) async {
    try {
      await _ensureInitialized();
      _logger.d('Analyzing iCloud attachments');

      // Get messages from INBOX using the IMAP client directly
      final mailbox = await _imapClient.selectInbox();
      if (mailbox.messagesExists == 0) {
        return {
          'hasAttachments': false,
          'totalAttachments': 0,
          'emailsWithAttachments': 0,
          'sampleSize': 0,
        };
      }

      final messageCount = mailbox.messagesExists;
      final fetchLimit = maxSample < messageCount ? maxSample : messageCount;
      final startIndex = messageCount > fetchLimit ? messageCount - fetchLimit + 1 : 1;
      final endIndex = messageCount;

      _logger.d('Fetching messages $startIndex to $endIndex from INBOX for attachment analysis');

      final fetchResult = await _imapClient.fetchMessages(
        MessageSequence.fromRange(startIndex, endIndex),
        'BODY.PEEK[]',
      );

      final messages = fetchResult.messages;
      int attachmentCount = 0;
      int emailsWithAttachments = 0;

      for (final message in messages) {
        final hasAttachments = _hasAttachments(message);
        if (hasAttachments) {
          emailsWithAttachments++;
          attachmentCount += _countAttachments(message);
        }
      }

      final result = {
        'hasAttachments': attachmentCount > 0,
        'totalAttachments': attachmentCount,
        'emailsWithAttachments': emailsWithAttachments,
        'sampleSize': messages.length,
      };

      _logger.i('iCloud attachment analysis: $result');
      return result;
    } catch (e, stack) {
      _logger.e('Error analyzing iCloud attachments', error: e, stackTrace: stack);
      return {
        'hasAttachments': false,
        'totalAttachments': 0,
        'emailsWithAttachments': 0,
        'sampleSize': 0,
      };
    }
  }

  // =======================================================
  // = Helper Functions for attachment analysis
  // =======================================================
  bool _hasAttachments(MimeMessage message) {
    try {
      final attachmentInfo = message.findContentInfo(disposition: ContentDisposition.attachment);
      return attachmentInfo.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  int _countAttachments(MimeMessage message) {
    try {
      // Count all parts with attachment disposition
      int count = 0;
      for (final part in message.allPartsFlat) {
        if (part.getHeaderContentDisposition()?.disposition == ContentDisposition.attachment) {
          count++;
        }
      }
      return count;
    } catch (e) {
      return 0;
    }
  }

  // =======================================================
  // = Function: dispose
  // = Description: Cleans up resources
  // =======================================================
  Future<void> dispose() async {
    try {
      if (_initialized) {
        await _imapClient.disconnect();
        _initialized = false;
        _logger.i('iCloud API service disposed');
      }
    } catch (e) {
      _logger.e('Error disposing iCloud API service', error: e);
    }
  }

  /// Delete multiple messages by UID
  Future<void> deleteMessages(List<String> ids) async {
    await _ensureInitialized();
    if (ids.isEmpty) return;
    final uids = ids.map((id) => int.tryParse(id)).whereType<int>().toList();
    if (uids.isEmpty) return;
    try {
      final sequence = MessageSequence.fromIds(uids);
      await _imapClient.markDeleted(sequence);
      await _imapClient.expunge();
      _logger.i('Deleted 24{uids.length} messages (iCloud)');
    } catch (e) {
      _logger.e('Error deleting messages (iCloud): $e');
      rethrow;
    }
  }
}