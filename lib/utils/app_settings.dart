/*
=======================================================
= File: app_settings.dart
= Project: LavaMail
= Description:
=   - Centralized management of all application and user settings using Hive for persistent storage.
=   - Provides static methods for getting/setting preferences (language, sync, notifications, network, etc.).
=   - Defines enums and data classes for settings and supported languages.
=   - Ensures consistent access and modification of app configuration across the project.
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/
import 'package:hive/hive.dart';
import 'dart:ui';


// =======================================================
// = Enums for Settings
// =======================================================
enum AppNetworkType { wifi, mobile, any }
enum AppSyncMode { auto, manual, off }

// =======================================================
// = Class : AppSettings
// = Description : Centralized settings management for the LavaMail application
// =======================================================
class AppSettings {
  static const String _boxName = 'app_settings';



  // =======================================================
  // = Function : getLanguage
  // = Description : Returns the current language code. Defaults to system language if supported, otherwise 'en'.
  // =======================================================
  static Future<String> getLanguage() async {
    final box = await Hive.openBox(_boxName);
    final savedLanguage = box.get('languageCode');
    if (savedLanguage == null) {
      final systemLocale = PlatformDispatcher.instance.locale.languageCode;
      if (isLanguageSupported(systemLocale)) {
        return systemLocale;
      }
      return 'en';
    }
    return savedLanguage as String;
  }

  // =======================================================
  // = Function : setLanguage
  // = Description : Sets the language code in persistent storage.
  // =======================================================
  static Future<void> setLanguage(String code) async {
    final box = await Hive.openBox(_boxName);
    await box.put('languageCode', code);
  }

  // =======================================================
  // = Function : getSavedLocale
  // = Description : Returns the saved locale as a Locale object.
  // =======================================================
  static Future<Locale> getSavedLocale() async {
    final languageCode = await getLanguage();
    return Locale(languageCode);
  }

  // =======================================================
  // = Function : saveLocale
  // = Description : Saves a Locale object as the preferred language in persistent storage.
  // =======================================================
  static Future<void> saveLocale(Locale locale) async => setLanguage(locale.languageCode);

  // =======================================================
  // = Function : getNotificationsEnabled
  // = Description : Returns whether notifications are enabled. Defaults to true.
  // =======================================================
  static Future<bool> getNotificationsEnabled() async {
    final box = await Hive.openBox(_boxName);
    return box.get('notificationsEnabled', defaultValue: true) as bool;
  }

  // =======================================================
  // = Function : setNotificationsEnabled
  // = Description : Sets whether notifications are enabled in persistent storage.
  // =======================================================
  static Future<void> setNotificationsEnabled(bool enabled) async {
    final box = await Hive.openBox(_boxName);
    await box.put('notificationsEnabled', enabled);
  }

  // =======================================================
  // = Function : getAutoLogout
  // = Description : Returns the auto logout duration in minutes. Defaults to 0 (disabled).
  // =======================================================
  static Future<int> getAutoLogout() async {
    final box = await Hive.openBox(_boxName);
    return box.get('autoLogout', defaultValue: 0) as int;
  }

  // =======================================================
  // = Function : setAutoLogout
  // = Description : Sets the auto logout duration in minutes. 0 disables auto logout.
  // =======================================================
  static Future<void> setAutoLogout(int minutes) async {
    final box = await Hive.openBox(_boxName);
    await box.put('autoLogout', minutes);
  }

  // =======================================================
  // = Function : getNetworkType
  // = Description : Returns the preferred network type for sync (wifi, mobile, any). Defaults to wifi.
  // =======================================================
  static Future<AppNetworkType> getNetworkType() async {
    final box = await Hive.openBox(_boxName);
    final value = box.get('networkType', defaultValue: 'wifi') as String;
    switch (value) {
      case 'mobile': return AppNetworkType.mobile;
      case 'any': return AppNetworkType.any;
      default: return AppNetworkType.wifi;
    }
  }

  // =======================================================
  // = Function : setNetworkType
  // = Description : Sets the preferred network type for sync in persistent storage.
  // =======================================================
  static Future<void> setNetworkType(AppNetworkType type) async {
    final box = await Hive.openBox(_boxName);
    String value;
    switch (type) {
      case AppNetworkType.mobile: value = 'mobile'; break;
      case AppNetworkType.any: value = 'any'; break;
      default: value = 'wifi';
    }
    await box.put('networkType', value);
  }

  // =======================================================
  // = Function : getSyncMode
  // = Description : Returns the current sync mode (auto, manual, off). Defaults to auto.
  // =======================================================
  static Future<AppSyncMode> getSyncMode() async {
    final box = await Hive.openBox(_boxName);
    final value = box.get('syncMode', defaultValue: 'auto') as String;
    switch (value) {
      case 'manual': return AppSyncMode.manual;
      case 'off': return AppSyncMode.off;
      default: return AppSyncMode.auto;
    }
  }

  // =======================================================
  // = Function : setSyncMode
  // = Description : Sets the sync mode in persistent storage.
  // =======================================================
  static Future<void> setSyncMode(AppSyncMode mode) async {
    final box = await Hive.openBox(_boxName);
    String value;
    switch (mode) {
      case AppSyncMode.manual: value = 'manual'; break;
      case AppSyncMode.off: value = 'off'; break;
      default: value = 'auto';
    }
    await box.put('syncMode', value);
  }

  // =======================================================
  // = Function : getSyncFrequency
  // = Description : Returns the sync frequency in minutes. Defaults to 15 minutes.
  // =======================================================
  static Future<int> getSyncFrequency() async {
    final box = await Hive.openBox(_boxName);
    return box.get('syncFrequency', defaultValue: 15) as int;
  }

  // =======================================================
  // = Function : setSyncFrequency
  // = Description : Sets the sync frequency in minutes in persistent storage.
  // =======================================================
  static Future<void> setSyncFrequency(int minutes) async {
    final box = await Hive.openBox(_boxName);
    await box.put('syncFrequency', minutes);
  }

  // =======================================================
  // = Function : getNetworkSettings
  // = Description : Returns all network-related settings as a map (networkType, syncMode, syncFrequency).
  // =======================================================
  static Future<Map<String, dynamic>> getNetworkSettings() async {
    final box = await Hive.openBox(_boxName);
    return {
      'networkType': await getNetworkType(),
      'syncMode': await getSyncMode(),
      'syncFrequency': box.get('syncFrequency', defaultValue: 15),
    };
  }

  // =======================================================
  // = Function : resetToDefaults
  // = Description : Resets all settings to their default values. This clears all user preferences.
  // =======================================================
  static Future<void> resetToDefaults() async {
    final box = await Hive.openBox(_boxName);
    await box.clear();
  }

  // =======================================================
  // = Function : getAllSettings
  // = Description : Returns all current settings as a map. Useful for debugging or export.
  // =======================================================
  static Future<Map<String, dynamic>> getAllSettings() async {
    final box = await Hive.openBox(_boxName);
    return Map<String, dynamic>.from(box.toMap());
  }

  // =======================================================
  // = Function : isLanguageSupported
  // = Description : Checks if a language code is supported by the application.
  // =======================================================
  static bool isLanguageSupported(String languageCode) {
    const supportedLanguages = ['en', 'fr', 'es', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko'];
    return supportedLanguages.contains(languageCode);
  }

  // =======================================================
  // = Function : getCleaningFolders
  // = Description : Returns the list of folders selected for quick cleaning.
  // =======================================================
  static Future<List<String>> getCleaningFolders() async {
    final box = await Hive.openBox(_boxName);
    final list = box.get('cleaningFolders');
    if (list is List) {
      return List<String>.from(list);
    }
    return [];
  }

  // =======================================================
  // = Function : setCleaningFolders
  // = Description : Saves the list of folders selected for quick cleaning.
  // =======================================================
  static Future<void> setCleaningFolders(List<String> folders) async {
    final box = await Hive.openBox(_boxName);
    await box.put('cleaningFolders', folders);
  }

  // =======================================================
  // = Function : getCleaningSenders
  // = Description : Returns the list of senders selected for quick cleaning.
  // =======================================================
  static Future<List<String>> getCleaningSenders() async {
    final box = await Hive.openBox(_boxName);
    final list = box.get('cleaningSenders');
    if (list is List) {
      return List<String>.from(list);
    }
    return [];
  }

  // =======================================================
  // = Function : setCleaningSenders
  // = Description : Saves the list of senders selected for quick cleaning.
  // =======================================================
  static Future<void> setCleaningSenders(List<String> senders) async {
    final box = await Hive.openBox(_boxName);
    await box.put('cleaningSenders', senders);
  }
}
