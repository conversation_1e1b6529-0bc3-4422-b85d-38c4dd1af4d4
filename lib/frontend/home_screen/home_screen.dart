import 'package:flutter/material.dart';
import '../../core/gmail/online_mode/gmail_online_mode.dart' as online;

import 'package:google_sign_in/google_sign_in.dart';
import '../../utils/app_settings.dart';

import 'widgets/home_screen_menu_widget.dart';
import 'widgets/home_screen_profile_widget.dart';
import 'widgets/home_screen_categorie_widget.dart';
import 'widgets/home_screen_information_widget.dart';
import 'widgets/home_screen_quick_clean_button.dart';

import 'package:provider/provider.dart';
import 'package:lavamail/providers/user_provider.dart';
import '../theme/app_theme.dart';
import '../theme/widgets/common_widgets.dart';

import '../../utils/services/storage/email_preload_service.dart';
import '../../utils/services/lifecycle/auto_logout_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../core/gmx/online_mode/gmx_online_mode.dart' as gmx;

// =======================================================
// = Function : HomeScreen
// = Description : Main home screen that displays Gmail categories and manages navigation
// =======================================================
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});
  @override
  HomeScreenState createState() => HomeScreenState();
}

// =======================================================
// = Function : HomeScreenState
// = Description : State management for home screen with Gmail API integration and drawer navigation
// =======================================================
class HomeScreenState extends State<HomeScreen> {
  dynamic mailApiService; // Can be GmailApiService or ProtonMailApiService
  int totalEmailCount = 0;
  List<dynamic> labels = [];
  GoogleSignInAccount? _user;
  AutoLogoutService? _autoLogoutService;
  online.AutoRefreshService? _autoRefreshService;


  Map<String, dynamic>? inboxStats;
  Map<String, dynamic>? sentStats;
  Map<String, dynamic>? spamStats;
  Map<String, dynamic>? trashStats;
  Map<String, dynamic>? socialStats;
  Map<String, dynamic>? notificationsStats;
  Map<String, dynamic>? forumsStats;
  Map<String, dynamic>? promotionsStats;
  List<Map<String, dynamic>>? labelStats;

  @override
  void initState() {
    super.initState();
    _initializeOnlineMode();
    _getCurrentUser();
    _initAutoLogout();
    _initAutoRefresh();
  }

  /// Initialise l'application en mode online
  void _initializeOnlineMode() async {
    try {
      debugPrint('[HOME] Application started in ONLINE mode');
    } catch (e) {
      debugPrint('[HOME] Error setting online mode: $e');
    }
  }

  void _getCurrentUser() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (userProvider.userType == UserType.gmx) {
      // Handle GMX user
      final gmxUser = userProvider.gmxUser;
      setState(() {
        _user = null; // No Google user for GMX
        mailApiService = gmx.GmxApiService(gmxUser);
        if (gmxUser != null) {
          _getRemoteTotalEmailCount();
          _fetchLabels();
          _fetchAllStats();
          _startBackgroundEmailLoading();
        }
      });
    } else {
      // Handle Gmail user
      final googleSignIn = GoogleSignIn();
      final user = await googleSignIn.signInSilently();
      setState(() {
        _user = user;
        mailApiService = online.GmailApiService(_user);
        if (_user != null) {
          _getRemoteTotalEmailCount();
          _fetchLabels();
          _fetchAllStats();
          _startBackgroundEmailLoading();
        }
      });
    }
  }

  /// Démarre le chargement des emails en arrière-plan
  void _startBackgroundEmailLoading() async {
    try {
      // Start email preloading for online mode
      _startEmailPreloading();
    } catch (e) {
      debugPrint('[HOME] Error starting background email loading: $e');
    }
  }

  /// Démarre le préchargement des emails pour le mode online
  void _startEmailPreloading() async {
    try {
      // Only start preloading for Gmail users
      if (_user != null) {
        final preloadService = EmailPreloadService();
        await preloadService.initialize(_user!);

        // Start preloading in background without waiting
        preloadService.startPreloading().then((_) {
          debugPrint('[HOME] Email preloading completed');
        }).catchError((e) {
          debugPrint('[HOME] Error during email preloading: $e');
        });
      } else {
        debugPrint('[HOME] Email preloading skipped - not a Gmail user');
      }
    } catch (e) {
      debugPrint('[HOME] Error starting email preloading: $e');
    }
  }

  void _getRemoteTotalEmailCount() async {
    try {
      int count = await mailApiService.getTotalEmailCount();
      setState(() {
        totalEmailCount = count;
      });
    } catch (e) {
      debugPrint('Error fetching email count: $e');
    }
  }

  void _fetchLabels() async {
    try {
      var labels = await mailApiService.getLabels();
      setState(() {
        this.labels = labels;
      });
    } catch (e) {
      debugPrint('Error fetching labels: $e');
    }
  }

  void _fetchAllStats() async {
    try {
      debugPrint('[HOME] Fetching stats sequentially to avoid quota limits...');
      if (mounted) {
        final inboxStats = await mailApiService.getInboxStats();
        if (mounted) {
          setState(() {
            this.inboxStats = inboxStats;
          });
        }
        await Future.delayed(const Duration(milliseconds: 500));
        final spamStats = await mailApiService.getSpamStats();
        if (mounted) {
          setState(() {
            this.spamStats = spamStats;
          });
        }
      }
      debugPrint('[HOME] Essential stats loaded successfully');
    } catch (e) {
      debugPrint('Error fetching stats: $e');
      if (e.toString().contains('Quota exceeded')) {
        debugPrint('[HOME] Quota exceeded - stats will be loaded on demand');
      }
    }
  }

  void _initAutoLogout() async {
    // Charger le paramètre d'auto logout de l'utilisateur
    final autoLogoutMinutes = await AppSettings.getAutoLogout();

    // Si auto logout est désactivé (0), ne pas démarrer le service
    if (autoLogoutMinutes > 0) {
      _autoLogoutService = AutoLogoutService(
        minutes: autoLogoutMinutes,
        onLogout: _handleSessionExpired,
      );
      _autoLogoutService!.startMonitoring();
    }
  }

  void _initAutoRefresh() async {
    // Charger le paramètre de rafraîchissement automatique
    final autoRefreshMinutes = 0; // TODO: Implement auto refresh setting in AppSettings
    // final autoRefreshMinutes = await AppSettings.getAutoRefresh();

    // Si rafraîchissement automatique est activé (>= 5 min)
    if (autoRefreshMinutes >= 5) {
      _autoRefreshService = online.AutoRefreshService(
        minutes: autoRefreshMinutes,
        onRefresh: _handleAutoRefresh,
      );
      _autoRefreshService!.startMonitoring();
      debugPrint('[AUTO_REFRESH] Service started with $autoRefreshMinutes minutes interval');
    } else {
      // Arrêter le service si les conditions ne sont pas remplies
      _autoRefreshService?.stopMonitoring();
      _autoRefreshService = null;
      if (autoRefreshMinutes < 5) {
        debugPrint('[AUTO_REFRESH] Service stopped - interval too short ($autoRefreshMinutes min)');
      }
    }
  }

  void _handleAutoRefresh() {
    // Déclencher un rafraîchissement des données
    if (mounted) {
      setState(() {
        // Forcer le rechargement des widgets en changeant leurs clés
        _refreshAllWidgets();
      });
    }
  }

  void _refreshAllWidgets() async {
    // Cette méthode sera appelée pour rafraîchir tous les widgets
    // Vider le cache de l'API online
    online.GmailApiService.clearCache();
    // Recharger les statistiques
    _fetchAllStats();
  }



  void _handleSessionExpired() {
    // Handle automatic logout on session timeout
    Provider.of<UserProvider>(context, listen: false).clearUser();
    Navigator.pushReplacementNamed(context, '/login');
  }





  @override
  void dispose() {
    _autoLogoutService?.stopMonitoring();
    _autoRefreshService?.stopMonitoring();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // Check if any user is connected (Gmail, GMX, Yahoo, or iCloud)
    final hasUser = _user != null ||
                   userProvider.gmxUser != null ||
                   userProvider.yahooUser != null ||
                   userProvider.icloudUser != null;

    if (!hasUser) {
      return const LavaMailScreen(
        title: 'Loading...',
        child: Center(child: CircularProgressIndicator()),
      );
    }

    final l10n = AppLocalizations.of(context);
    return LavaMailScreen(
      title: l10n.appName,
      backgroundColor: LavaMailTheme.backgroundColor,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Section - Widget implémenté dans home_screen_profile_widget.dart
            HomeScreenProfileWidget(),

            // Email Information Section - Widget implémenté dans home_screen_information_widget.dart
            HomeScreenInformationWidget(),

            // Categories Section - Widget implémenté dans home_screen_categorie_widget.dart
            HomeScreenCategorieWidget(),

            // Quick Clean Buttons Section
            FutureBuilder<List<String>>(
              future: AppSettings.getCleaningFolders(),
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const SizedBox.shrink();
                }
                final folders = snapshot.data!;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                      child: Text('Nettoyage rapide', style: Theme.of(context).textTheme.titleMedium),
                    ),
                    ...folders.map((folder) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                      child: HomeScreenQuickCleanButton(
                        labelQuery: folder,
                        labelName: folder,
                      ),
                    )),
                  ],
                );
              },
            ),

            // Menu Widget - Navigation entre Home, Stats et Settings
            MenuWidget(
              currentRoute: '/home',
            ),
          ],
        ),
      ),
    );
  }
}
