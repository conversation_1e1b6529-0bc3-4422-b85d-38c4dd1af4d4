import 'package:flutter/material.dart';
import '../app_theme.dart';

/// Common reusable widgets for maintaining visual consistency across the app
/// This file should only contain widgets that are used in multiple screens/contexts

/// Wrapper widget that enforces portrait orientation for the entire app
class PortraitOnlyWrapper extends StatefulWidget {
  final Widget child;

  const PortraitOnlyWrapper({
    super.key,
    required this.child,
  });

  @override
  State<PortraitOnlyWrapper> createState() => _PortraitOnlyWrapperState();
}

class _PortraitOnlyWrapperState extends State<PortraitOnlyWrapper> {
  @override
  void initState() {
    super.initState();
    // Force portrait orientation when this widget is created
    LavaMailTheme.setPortraitOrientation();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Screen wrapper that ensures portrait orientation and provides consistent layout
class LavaMailScreen extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showAppBar;
  final Color? backgroundColor;

  const LavaMailScreen({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.floatingActionButton,
    this.showAppBar = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return PortraitOnlyWrapper(
      child: Scaffold(
        backgroundColor: backgroundColor ?? LavaMailTheme.backgroundColor,
        appBar: showAppBar && title != null
            ? AppBar(
                title: Text(title!),
                actions: actions,
              )
            : null,
        body: SafeArea(
          child: child,
        ),
        floatingActionButton: floatingActionButton,
      ),
    );
  }
}

/// Primary button with consistent styling
class LavaMailButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final IconData? icon;
  final double? width;

  const LavaMailButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.icon,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: LavaMailTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (icon != null) ...[
                      Icon(icon, size: 20),
                      const SizedBox(width: 8),
                    ],
                    Expanded(
                      child: Text(text, style: const TextStyle(fontSize: 16)),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}

/// Bouton secondaire avec bordure
class LavaMailOutlinedButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final IconData? icon;

  const LavaMailOutlinedButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: LavaMailTheme.primaryColor),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, size: 20),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(text, style: TextStyle(fontSize: 16, color: LavaMailTheme.primaryColor)),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error message widget with consistent styling
class LavaMailErrorMessage extends StatelessWidget {
  final String message;

  const LavaMailErrorMessage({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(LavaMailTheme.spacingM),
      decoration: BoxDecoration(
        color: LavaMailTheme.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
        border: Border.all(color: LavaMailTheme.errorColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: LavaMailTheme.errorColor,
            size: 20,
          ),
          const SizedBox(width: LavaMailTheme.spacingS),
          Expanded(
            child: Text(
              message,
              style: LavaMailTheme.errorText,
            ),
          ),
        ],
      ),
    );
  }
}

/// Loading indicator with consistent styling
class LavaMailLoadingIndicator extends StatelessWidget {
  final String? message;
  final double size;

  const LavaMailLoadingIndicator({
    super.key,
    this.message,
    this.size = 20,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: size,
          width: size,
          child: CircularProgressIndicator(
            color: LavaMailTheme.primaryColor,
            strokeWidth: 2,
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: LavaMailTheme.spacingS),
          Text(
            message!,
            style: LavaMailTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Text form field with consistent styling
class LavaMailTextFormField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? suffixIcon;
  final bool enabled;
  final bool filled;
  final Color? fillColor;

  const LavaMailTextFormField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.obscureText = false,
    this.suffixIcon,
    this.enabled = true,
    this.filled = false,
    this.fillColor,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      keyboardType: keyboardType,
      obscureText: obscureText,
      enabled: enabled,
      style: LavaMailTheme.bodyMedium,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: LavaMailTheme.labelText,
        hintText: hint,
        hintStyle: LavaMailTheme.bodySmall,
        suffixIcon: suffixIcon,
        filled: filled,
        fillColor: fillColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
          borderSide: BorderSide(color: LavaMailTheme.textLightColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
          borderSide: BorderSide(color: LavaMailTheme.textLightColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
          borderSide: BorderSide(color: LavaMailTheme.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
          borderSide: BorderSide(color: LavaMailTheme.errorColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
          borderSide: BorderSide(color: LavaMailTheme.errorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: LavaMailTheme.spacingM,
          vertical: LavaMailTheme.spacingM,
        ),
      ),
    );
  }
}

/// Base modal widget with consistent styling
class LavaMailModal extends StatelessWidget {
  final String title;
  final Widget content;
  final List<Widget>? actions;
  final VoidCallback? onBack;
  final IconData? titleIcon;

  const LavaMailModal({
    super.key,
    required this.title,
    required this.content,
    this.actions,
    this.onBack,
    this.titleIcon,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final maxHeight = screenSize.height * 0.85; // 85% de la hauteur de l'écran
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusL),
      ),
      elevation: 8,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: LavaMailTheme.modalMaxWidth,
          maxHeight: maxHeight,
        ),
        child: Padding(
          padding: const EdgeInsets.all(LavaMailTheme.modalPadding),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Row(
                children: [
                  if (onBack != null) ...[
                    IconButton(
                      onPressed: onBack,
                      icon: const Icon(Icons.arrow_back),
                      style: IconButton.styleFrom(
                        foregroundColor: LavaMailTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(width: LavaMailTheme.spacingS),
                  ],
                  if (titleIcon != null) ...[
                    Icon(
                      titleIcon,
                      color: LavaMailTheme.primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: LavaMailTheme.spacingM),
                  ],
                  Expanded(
                    child: Text(
                      title,
                      style: LavaMailTheme.headingMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: LavaMailTheme.spacingL),
              
              // Content
              Flexible(
                child: SingleChildScrollView(
                  child: content,
                ),
              ),
              
              // Actions
              if (actions != null && actions!.isNotEmpty) ...[
                const SizedBox(height: LavaMailTheme.spacingL),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!.map((action) => Padding(
                    padding: const EdgeInsets.only(left: LavaMailTheme.spacingS),
                    child: action,
                  )).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Provider selection button with consistent styling
class LavaMailProviderButton extends StatelessWidget {
  final String providerName;
  final String assetPath;
  final VoidCallback onTap;
  final bool isLoading;

  const LavaMailProviderButton({
    super.key,
    required this.providerName,
    required this.assetPath,
    required this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      enabled: !isLoading,
      label: 'Sign in with $providerName',
      child: Material(
        color: LavaMailTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusM),
          side: BorderSide(
            color: LavaMailTheme.textLightColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        elevation: 2,
        shadowColor: LavaMailTheme.textLightColor.withValues(alpha: 0.2),
        child: InkWell(
          borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusM),
          onTap: isLoading ? null : onTap,
          child: Container(
            width: 80,
            height: 90, // Augmenté de 80 à 90 pour éviter l'overflow
            padding: const EdgeInsets.all(LavaMailTheme.spacingS), // Réduit le padding
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min, // Ajouté pour optimiser l'espace
              children: [
                if (isLoading)
                  const LavaMailLoadingIndicator(size: 24)
                else ...[
                  Image.asset(
                    assetPath,
                    width: 28, // Réduit de 32 à 28
                    height: 28, // Réduit de 32 à 28
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.email,
                      size: 28, // Réduit de 32 à 28
                      color: LavaMailTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 4), // Réduit l'espacement
                  Flexible( // Ajouté Flexible pour éviter l'overflow du texte
                    child: Text(
                      providerName,
                      style: LavaMailTheme.bodySmall.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 11, // Réduit légèrement la taille de police
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}



// NOTE: Email-specific widgets (OptimizedEmailList, OptimizedGroupedEmailList, EmailCard)
// have been moved to lib/frontend/gmail_screen/widgets/gmail_screen_widgets.dart
// as they are specific to email management functionality

/// Badge de notification
class LavaMailBadge extends StatelessWidget {
  final int count;
  final Color? color;

  const LavaMailBadge({
    super.key,
    required this.count,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (count <= 0) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color ?? LavaMailTheme.secondaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      constraints: const BoxConstraints(minWidth: 20, minHeight: 20),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

/// Widget d'état de chargement
class LoadingIndicator extends StatelessWidget {
  final String? message;

  const LoadingIndicator({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            color: LavaMailTheme.primaryColor,
          ),
          if (message != null)
            Padding(
              padding: const EdgeInsets.only(top: LavaMailTheme.spacingM),
              child: Text(
                message!,
                style: TextStyle(
                  color: LavaMailTheme.textSecondaryColor,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Widget d'état vide
class EmptyStateWidget extends StatelessWidget {
  final String message;
  final IconData icon;
  final Widget? action;

  const EmptyStateWidget({
    super.key,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(LavaMailTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 80,
              color: LavaMailTheme.textLightColor,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: LavaMailTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null)
              Padding(
                padding: const EdgeInsets.only(top: LavaMailTheme.spacingL),
                child: action,
              ),
          ],
        ),
      ),
    );
  }
}

/// AppBar standardisée
class LavaMailAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const LavaMailAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      centerTitle: false,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      actions: actions,
      elevation: 2,
      backgroundColor: LavaMailTheme.primaryColor,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Drawer standardisé
class LavaMailDrawer extends StatelessWidget {
  final String userName;
  final String userEmail;
  final String? userAvatarUrl;
  final List<LavaMailDrawerItem> items;
  final ValueChanged<int> onItemTap;
  final int selectedIndex;

  const LavaMailDrawer({
    super.key,
    required this.userName,
    required this.userEmail,
    this.userAvatarUrl,
    required this.items,
    required this.onItemTap,
    required this.selectedIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(userName),
            accountEmail: Text(userEmail),
            currentAccountPicture: CircleAvatar(
              backgroundImage: userAvatarUrl != null
                  ? NetworkImage(userAvatarUrl!)
                  : null,
              child: userAvatarUrl == null
                  ? Text(
                      userName.isNotEmpty ? userName[0].toUpperCase() : '?',
                      style: const TextStyle(fontSize: 24),
                    )
                  : null,
            ),
            decoration: BoxDecoration(
              color: LavaMailTheme.primaryColor,
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: items.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = index == selectedIndex;

                return ListTile(
                  leading: Icon(
                    item.icon,
                    color: isSelected
                        ? LavaMailTheme.primaryColor
                        : LavaMailTheme.textSecondaryColor,
                  ),
                  title: Text(
                    item.title,
                    style: TextStyle(
                      color: isSelected
                          ? LavaMailTheme.primaryColor
                          : LavaMailTheme.textPrimaryColor,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  trailing: item.badgeCount > 0
                      ? LavaMailBadge(count: item.badgeCount)
                      : null,
                  selected: isSelected,
                  selectedTileColor: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
                  onTap: () => onItemTap(index),
                );
              },
            ),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
    );
  }
}

class LavaMailDrawerItem {
  final String title;
  final IconData icon;
  final int badgeCount;

  const LavaMailDrawerItem({
    required this.title,
    required this.icon,
    this.badgeCount = 0,
  });
}
