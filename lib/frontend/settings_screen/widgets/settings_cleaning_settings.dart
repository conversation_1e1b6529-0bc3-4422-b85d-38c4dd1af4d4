import 'package:flutter/material.dart';
import '../../../providers/user_provider.dart';
import '../../../core/gmail/online_mode/gmail_online_mode.dart';
import '../../../core/gmx/online_mode/gmx_online_mode.dart';
import '../../../utils/app_settings.dart';

class SettingsCleaningSettings extends StatefulWidget {
  final UserProvider userProvider;
  final List<String> selectedFolders;
  final List<String> selectedSenders;
  final ValueChanged<List<String>> onFoldersChanged;
  final ValueChanged<List<String>> onSendersChanged;

  const SettingsCleaningSettings({
    super.key,
    required this.userProvider,
    required this.selectedFolders,
    required this.selectedSenders,
    required this.onFoldersChanged,
    required this.onSendersChanged,
  });

  @override
  State<SettingsCleaningSettings> createState() => _SettingsCleaningSettingsState();
}

class _SettingsCleaningSettingsState extends State<SettingsCleaningSettings> {
  List<String> availableFolders = [];
  List<String> availableSenders = [];
  bool isLoading = true;
  late List<String> _selectedFolders;
  late List<String> _selectedSenders;
  final TextEditingController _senderController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSelections();
    _loadFoldersAndSenders();
  }

  Future<void> _loadSelections() async {
    final folders = (await AppSettings.getCleaningFolders()).whereType<String>().toList();
    final senders = (await AppSettings.getCleaningSenders()).whereType<String>().toList();
    setState(() {
      _selectedFolders = folders;
      _selectedSenders = senders;
    });
  }

  void _onFoldersChanged(List<String> folders) {
    setState(() {
      _selectedFolders = folders;
    });
    AppSettings.setCleaningFolders(folders);
    widget.onFoldersChanged(folders);
  }

  void _onSendersChanged(List<String> senders) {
    setState(() {
      _selectedSenders = senders;
    });
    AppSettings.setCleaningSenders(senders);
    widget.onSendersChanged(senders);
  }

  Future<void> _loadFoldersAndSenders() async {
    setState(() { isLoading = true; });
    final userType = widget.userProvider.userType;
    List<String> folders = [];
    List<String> senders = [];
    try {
      switch (userType) {
        case UserType.gmail:
          final gmailService = GmailApiService(widget.userProvider.user!);
          final labels = await gmailService.getLabels();
          folders = labels.map((l) => l.name ?? '').where((name) => name.isNotEmpty).toList();
          // Pour les expéditeurs, il faudrait parcourir les messages récents et extraire les "from"
          // (à implémenter plus tard)
          break;
        case UserType.gmx:
          final gmxService = GmxApiService(widget.userProvider.gmxUser!);
          final gmxFolders = await gmxService.getAllFolders();
          folders = gmxFolders.map((f) => f['displayName'] as String? ?? f['name'] as String).toList();
          // Idem pour les expéditeurs (à implémenter plus tard)
          break;
        case UserType.yahoo:
          // À compléter : récupération des dossiers Yahoo
          // final yahooService = YahooApiService(widget.userProvider.yahooUser!);
          // folders = ...
          break;
        case UserType.icloud:
          // À compléter : récupération des dossiers iCloud
          // final icloudService = IcloudApiService(widget.userProvider.icloudUser!);
          // folders = ...
          break;
      }
    } catch (e) {
      // Gérer les erreurs de chargement
    }
    setState(() {
      availableFolders = folders;
      availableSenders = senders;
      isLoading = false;
    });
  }

  void _addSender(String sender) {
    if (sender.isEmpty || _selectedSenders.contains(sender)) return;
    setState(() {
      _selectedSenders.add(sender);
      _onSendersChanged(_selectedSenders);
      _senderController.clear();
    });
  }

  void _removeSender(String sender) {
    setState(() {
      _selectedSenders.remove(sender);
      _onSendersChanged(_selectedSenders);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Dossiers à nettoyer', style: Theme.of(context).textTheme.titleMedium),
        Wrap(
          spacing: 8,
          children: availableFolders.map((folder) => FilterChip(
            label: Text(folder),
            selected: _selectedFolders.contains(folder),
            onSelected: (selected) {
              final updated = List<String>.from(_selectedFolders);
              if (selected) {
                updated.add(folder);
              } else {
                updated.remove(folder);
              }
              _onFoldersChanged(updated);
            },
          )).toList(),
        ),
        const SizedBox(height: 16),
        Text('Expéditeurs à cibler (optionnel)', style: Theme.of(context).textTheme.titleMedium),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _senderController,
                decoration: const InputDecoration(
                  hintText: 'Ajouter un expéditeur',
                ),
                onSubmitted: _addSender,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _addSender(_senderController.text.trim()),
            ),
          ],
        ),
        Wrap(
          spacing: 8,
          children: _selectedSenders.map((sender) => Chip(
            label: Text(sender),
            onDeleted: () => _removeSender(sender),
          )).toList(),
        ),
      ],
    );
  }
} 