{"@@locale": "en", "appTitle": "<PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "settings": "Settings", "@settings": {"description": "Settings menu label"}, "sync": "Synchronization", "@sync": {"description": "Synchronization label"}, "language": "Language", "@language": {"description": "Language selection label"}, "english": "English", "@english": {"description": "English language option"}, "french": "French", "@french": {"description": "French language option"}, "german": "German", "@german": {"description": "German language option"}, "spanish": "Spanish", "@spanish": {"description": "Spanish language option"}, "portuguese": "Portuguese", "@portuguese": {"description": "Portuguese language option"}, "hindi": "Hindi", "@hindi": {"description": "Hindi language option"}, "indonesian": "Indonesian", "@indonesian": {"description": "Indonesian language option"}, "japanese": "Japanese", "@japanese": {"description": "Japanese language option"}, "italian": "Italian", "@italian": {"description": "Italian language option"}, "notifications": "Notifications", "@notifications": {"description": "Notifications settings label"}, "manualSync": "Manual sync now", "@manualSync": {"description": "But<PERSON> to trigger manual synchronization"}, "syncFrequency": "Sync frequency", "@syncFrequency": {"description": "Synchronization frequency setting label"}, "wifiOnly": "Sync on Wi-Fi only", "@wifiOnly": {"description": "Option to sync only when connected to Wi-Fi"}, "syncMode": "Sync mode", "@syncMode": {"description": "Synchronization mode setting label"}, "auto": "Auto", "@auto": {"description": "Automatic synchronization mode"}, "manual": "Manual", "@manual": {"description": "Manual synchronization mode"}, "dataMode": "Data Mode", "@dataMode": {"description": "Data access mode setting label"}, "offlineMode": "Offline", "@offlineMode": {"description": "Offline data mode"}, "onlineMode": "Online", "@onlineMode": {"description": "Online data mode"}, "offlineModeDesc": "Use local cache only. Faster but may not be up to date.", "@offlineModeDesc": {"description": "Description of offline mode"}, "onlineModeDesc": "Use Gmail API directly. Always up to date but slower.", "@onlineModeDesc": {"description": "Description of online mode"}, "massDelete": "Mass Delete", "@massDelete": {"description": "Mass delete action"}, "massUnsubscribe": "Mass Unsubscribe", "@massUnsubscribe": {"description": "Mass unsubscribe action"}, "sortBySender": "Sort by Sender", "@sortBySender": {"description": "Sort emails by sender"}, "sortByDate": "Sort by Date", "@sortByDate": {"description": "Sort emails by date"}, "emailSize": "<PERSON><PERSON>", "@emailSize": {"description": "Email size label"}, "totalSize": "Total Size", "@totalSize": {"description": "Total size label"}, "averageSize": "Average Size", "@averageSize": {"description": "Average size label"}, "clearCache": "Clear cache and database, then resynchronize", "@clearCache": {"description": "Button to clear cache and resynchronize"}, "languageChanged": "Language changed successfully", "@languageChanged": {"description": "Success message when language is changed"}, "selectLanguage": "Select your preferred language", "@selectLanguage": {"description": "Instruction to select preferred language"}, "syncSettings": "Sync Settings", "@syncSettings": {"description": "Synchronization settings section title"}, "generalSettings": "General Settings", "@generalSettings": {"description": "General settings section title"}, "simpleSecureEmail": "Simple and Secure Email", "@simpleSecureEmail": {"description": "App tagline or description"}, "signInCancelled": "Sign in was cancelled", "@signInCancelled": {"description": "Message when user cancels sign in"}, "signInFailed": "Failed to sign in", "@signInFailed": {"description": "Error message when sign in fails"}, "signingIn": "Signing in...", "@signingIn": {"description": "Loading message during sign in process"}, "continueWithGmail": "Continue with Gmail", "@continueWithGmail": {"description": "Button to continue with Gmail authentication"}, "appLogo": "App Logo", "@appLogo": {"description": "Accessibility label for app logo"}, "signInWithGmail": "Sign in with Gmail", "@signInWithGmail": {"description": "<PERSON><PERSON> to sign in with Gmail"}, "signInWithWeb": "Sign in with Web", "@signInWithWeb": {"description": "<PERSON><PERSON> to sign in using web browser"}, "webAuthDescription": "Sign in using your web browser", "@webAuthDescription": {"description": "Description for web authentication method"}, "openingBrowser": "Opening browser...", "@openingBrowser": {"description": "Message shown when opening web browser for authentication"}, "webAuthFailed": "Web authentication failed", "@webAuthFailed": {"description": "Error message when web authentication fails"}, "webAuthCancelled": "Web authentication was cancelled", "@webAuthCancelled": {"description": "Message when user cancels web authentication"}, "chooseSignInMethod": "Choose your sign-in method", "@chooseSignInMethod": {"description": "Header text for authentication method selection"}, "tapToSignIn": "Tap to sign in", "@tapToSignIn": {"description": "Text below the Gmail icon button"}, "inbox": "Inbox", "@inbox": {"description": "Inbox folder label"}, "loading": "Loading...", "@loading": {"description": "Generic loading message"}, "emails": "emails", "@emails": {"description": "Plural form of email"}, "spam": "Spam", "@spam": {"description": "Spam folder label"}, "spams": "spams", "@spams": {"description": "Plural form of spam"}, "labels": "Labels", "@labels": {"description": "Labels section title"}, "checkingDatabase": "Checking database...", "@checkingDatabase": {"description": "Message when checking local database"}, "offlineProcessing": "Offline processing", "@offlineProcessing": {"description": "Label for offline processing phase"}, "overallProgress": "Overall progress", "@overallProgress": {"description": "Label for overall synchronization progress"}, "calculatingTime": "Calculating remaining time...", "@calculatingTime": {"description": "Message when calculating estimated time remaining"}, "retry": "Retry", "@retry": {"description": "Button to retry an operation"}, "logout": "Logout", "@logout": {"description": "Button to logout from the application"}, "emailCount": "{count} {count, plural, =0{emails} =1{email} other{emails}}", "@emailCount": {"description": "Displays the count of emails with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of emails"}}}, "spamCount": "{count} {count, plural, =0{spams} =1{spam} other{spams}}", "@spamCount": {"description": "Displays the count of spam emails with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of spam emails"}}}, "delete": "Delete", "@delete": {"description": "Delete button label"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button label"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button label"}, "deleteSelectedMessages": "Delete selected messages?", "@deleteSelectedMessages": {"description": "Title for delete confirmation dialog"}, "deleteConfirmationMessage": "Are you sure you want to permanently delete the selected messages? This action cannot be undone.", "@deleteConfirmationMessage": {"description": "Confirmation message for deleting messages"}, "deleteLabel": "Delete Label", "@deleteLabel": {"description": "Title for delete label dialog"}, "deleteLabelConfirmation": "Are you sure you want to delete the label \"{labelName}\" and all associated emails? This action cannot be undone.", "@deleteLabelConfirmation": {"description": "Confirmation message for deleting a label", "placeholders": {"labelName": {"type": "String", "description": "Name of the label to delete"}}}, "messagesDeletedSuccessfully": "{count} {count, plural, =1{message} other{messages}} deleted successfully", "@messagesDeletedSuccessfully": {"description": "Success message after deleting messages", "placeholders": {"count": {"type": "int", "description": "Number of deleted messages"}}}, "synchronizationCompleted": "Synchronization completed!", "@synchronizationCompleted": {"description": "Message when synchronization is finished"}, "errorDuringLogout": "Error during logout", "@errorDuringLogout": {"description": "Error message when logout fails"}, "noInboxMessages": "No inbox messages found", "@noInboxMessages": {"description": "Message when inbox is empty"}, "groupBy": "Group: {groupType}", "@groupBy": {"description": "Label for grouping dropdown", "placeholders": {"groupType": {"type": "String", "description": "Type of grouping"}}}, "descending": "Descending", "@descending": {"description": "Descending sort order option"}, "ascending": "Ascending", "@ascending": {"description": "Ascending sort order option"}, "all": "All", "@all": {"description": "All items option"}, "messages": "messages", "@messages": {"description": "Plural form of message"}, "messageCount": "{count} {count, plural, =1{message} other{messages}}", "@messageCount": {"description": "Displays the count of messages with proper pluralization", "placeholders": {"count": {"type": "int", "description": "Number of messages"}}}, "deleteLabelTooltip": "Delete label and emails", "@deleteLabelTooltip": {"description": "Tooltip for delete label button"}, "refreshInbox": "Refresh inbox", "@refreshInbox": {"description": "Accessibility label for refresh button"}, "deleteSelectedEmails": "Delete selected emails", "@deleteSelectedEmails": {"description": "Accessibility label for delete button"}, "sortOrder": "Sort order", "@sortOrder": {"description": "Accessibility label for sort dropdown"}, "selectAll": "Select all", "@selectAll": {"description": "Accessibility label for select all checkbox"}, "labelDeletedSuccessfully": "Label and associated emails deleted successfully", "@labelDeletedSuccessfully": {"description": "Success message when label is deleted"}, "failedToDeleteLabel": "Failed to delete label", "@failedToDeleteLabel": {"description": "Error message when label deletion fails"}, "errorLoadingLabelEmails": "Error loading label emails", "@errorLoadingLabelEmails": {"description": "Error message when loading emails for a label fails"}, "errorDuringDeletion": "Error during deletion", "@errorDuringDeletion": {"description": "Generic error message during deletion"}, "noEmailsForLabel": "No emails for this label", "@noEmailsForLabel": {"description": "Message when a label has no emails"}, "unknownSender": "Unknown sender", "@unknownSender": {"description": "Placeholder for unknown email sender"}, "noSubject": "(No subject)", "@noSubject": {"description": "Placeholder for emails without subject"}, "from": "From", "@from": {"description": "Email from field label"}, "group": "Group", "@group": {"description": "Group label for dropdown"}, "sortBy": "Sort by", "@sortBy": {"description": "Sort by label for dropdown (used for grouping options)"}, "none": "None", "@none": {"description": "None option for grouping"}, "sender": "Sender", "@sender": {"description": "Sender option for grouping"}, "year": "Year", "@year": {"description": "Year option for grouping"}, "month": "Month", "@month": {"description": "Month option for grouping"}, "date": "Date", "@date": {"description": "Date option for sorting"}, "order": "Order", "@order": {"description": "Sort order label"}, "unsubscribe": "Unsubscribe", "@unsubscribe": {"description": "Unsubscribe button label"}, "massUnsubscribeScreenTitle": "Mass Unsubscribe", "@massUnsubscribeScreenTitle": {"description": "Mass unsubscribe screen title"}, "newslettersAndPromotions": "Newsletters & Promotions", "@newslettersAndPromotions": {"description": "Section title for newsletters and promotional emails"}, "detectingNewsletters": "Detecting newsletters...", "@detectingNewsletters": {"description": "Loading message when detecting newsletters"}, "noNewslettersFound": "No newsletters found", "@noNewslettersFound": {"description": "Message when no newsletters are detected"}, "unsubscribeSelected": "Unsubscribe from selected", "@unsubscribeSelected": {"description": "Button to unsubscribe from selected newsletters"}, "unsubscribeConfirmation": "Unsubscribe from {count} {count, plural, =1{newsletter} other{newsletters}}?", "@unsubscribeConfirmation": {"description": "Confirmation dialog for mass unsubscribe", "placeholders": {"count": {"type": "int", "description": "Number of newsletters to unsubscribe from"}}}, "unsubscribeWarning": "This will attempt to unsubscribe you from the selected newsletters. This action may take some time and cannot be undone.", "@unsubscribeWarning": {"description": "Warning message for mass unsubscribe"}, "unsubscribeInProgress": "Unsubscribing...", "@unsubscribeInProgress": {"description": "Message during unsubscribe process"}, "unsubscribeCompleted": "Unsubscribe completed", "@unsubscribeCompleted": {"description": "Success message after unsubscribe"}, "unsubscribePartialSuccess": "{successful} of {total} unsubscribe attempts successful", "@unsubscribePartialSuccess": {"description": "Message for partial success in mass unsubscribe", "placeholders": {"successful": {"type": "int", "description": "Number of successful unsubscribes"}, "total": {"type": "int", "description": "Total number of unsubscribe attempts"}}}, "emailsFromSender": "{count} {count, plural, =1{email} other{emails}} from this sender", "@emailsFromSender": {"description": "Shows number of emails from a specific sender", "placeholders": {"count": {"type": "int", "description": "Number of emails from sender"}}}, "lastReceived": "Last received: {date}", "@lastReceived": {"description": "Shows when the last email was received", "placeholders": {"date": {"type": "String", "description": "Date of last received email"}}}, "unsubscribeLinkFound": "Unsubscribe link found", "@unsubscribeLinkFound": {"description": "Indicates that an unsubscribe link was found in the email"}, "noUnsubscribeLink": "No unsubscribe link found", "@noUnsubscribeLink": {"description": "Indicates that no unsubscribe link was found in the email"}, "lastSyncTime": "Last sync: {date}", "@lastSyncTime": {"description": "Shows the date and time of the last synchronization", "placeholders": {"date": {"type": "String", "description": "Formatted date and time of last sync"}}}, "neverSynced": "Never synchronized", "@neverSynced": {"description": "Message when no synchronization has been performed yet"}, "syncTooFrequent": "Synchronization too frequent", "@syncTooFrequent": {"description": "Title for sync rate limit error dialog"}, "syncRateLimitMessage": "Please wait {minutes} minutes before performing another manual synchronization. This helps protect Gmail API limits and ensures optimal performance.", "@syncRateLimitMessage": {"description": "Message explaining sync rate limit", "placeholders": {"minutes": {"type": "int", "description": "Number of minutes to wait"}}}, "syncRateLimitShort": "Rate limit reached. Please wait {minutes} minutes.", "@syncRateLimitShort": {"placeholders": {"minutes": {"type": "int"}}}, "ok": "OK", "@ok": {"description": "OK button label"}, "countdownConfirmMessage": "You can confirm in {seconds} seconds...", "@countdownConfirmMessage": {"description": "Message shown in reset confirmation dialog with countdown before OK is enabled.", "placeholders": {"seconds": {"type": "int", "description": "Seconds remaining before confirmation is enabled"}}}, "almostDone": "Almost done...", "estimatedTimeHours": "Estimated time left: ~{hours}h {minutes}min", "estimatedTimeMinutes": "Estimated time left: ~{minutes}min", "estimatedTimeSeconds": "Estimated time left: ~{seconds}s", "@estimatedTimeHours": {"placeholders": {"hours": {"type": "int"}, "minutes": {"type": "int"}}}, "@estimatedTimeMinutes": {"placeholders": {"minutes": {"type": "int"}}}, "@estimatedTimeSeconds": {"placeholders": {"seconds": {"type": "int"}}}, "syncHistoryIdInvalid": "History ID invalid, performing full sync", "@syncHistoryIdInvalid": {"description": "Message shown when Gmail historyId is invalid and a full sync is triggered."}, "syncError": "Sync error: {error}", "@syncError": {"placeholders": {"error": {"type": "String"}}}, "syncingEmails": "Syncing {processed} / {total} emails...", "@syncingEmails": {"description": "Progression de la synchronisation", "placeholders": {"processed": {"type": "int"}, "total": {"type": "int"}}}, "noEmailsFound": "No emails found in this Gmail account.", "@noEmailsFound": {"description": "Message displayed when no emails are found during sync."}, "syncInProgress": "Sync in progress", "@syncInProgress": {"description": "Message displayed when manual sync is in progress."}, "checkingForUpdates": "Checking for updates...", "@checkingForUpdates": {"description": "Message displayed when checking for email updates."}, "performingIncrementalSync": "Performing incremental sync...", "@performingIncrementalSync": {"description": "Message displayed when performing incremental sync."}, "initialSyncMessage": "Initial sync: this may take several minutes depending on the number of emails. Please wait...", "@initialSyncMessage": {"description": "Message displayed during initial sync."}, "emailsSynced": "{count} {count, plural, =1{email} other{emails}} synced!", "@emailsSynced": {"description": "Message displayed when sync is completed.", "placeholders": {"count": {"type": "int"}}}, "syncCompletedNotificationTitle": "Sync Completed", "@syncCompletedNotificationTitle": {"description": "Title for sync completion notification."}, "syncCompletedNotificationBody": "Your emails have been synchronized successfully", "@syncCompletedNotificationBody": {"description": "Body text for sync completion notification."}, "autoSyncStartedTitle": "Auto sync started", "@autoSyncStartedTitle": {"description": "Title for auto sync started notification"}, "autoSyncStartedBody": "Synchronizing your emails in background...", "@autoSyncStartedBody": {"description": "Body text for auto sync started notification"}, "autoSyncCompletedTitle": "Auto sync completed", "@autoSyncCompletedTitle": {"description": "Title for auto sync completed notification"}, "autoSyncCompletedBody": "Auto synchronization completed successfully.", "@autoSyncCompletedBody": {"description": "Body text for auto sync completed notification"}, "autoSyncCompletedWithNewEmailsBody": "Auto sync completed. {count} new {count, plural, =1{email} other{emails}} found.", "@autoSyncCompletedWithNewEmailsBody": {"description": "Body text for auto sync completed notification with new emails count", "placeholders": {"count": {"type": "int", "description": "Number of new emails found"}}}, "dataModeOffline": "Offline", "@dataModeOffline": {"description": "Label for offline data mode"}, "dataModeOnline": "Online", "@dataModeOnline": {"description": "Label for online data mode"}, "dataModeOfflineDescription": "Uses locally synchronized emails", "@dataModeOfflineDescription": {"description": "Description for offline data mode in the switch dialog"}, "dataModeOnlineDescription": "Direct access to emails via Internet", "@dataModeOnlineDescription": {"description": "Description for online data mode in the switch dialog"}, "errorLoadingData": "Error loading data", "@errorLoadingData": {"description": "Error message shown when data loading fails"}, "checkInternetConnectionAndRetry": "Check your internet connection and retry", "@checkInternetConnectionAndRetry": {"description": "Prompt to check internet connection and retry"}, "retryLoadingLabels": "Retry loading labels", "noLabelsFound": "No labels found", "unsubscribeResults": "Unsubscribe Results", "successful": "✅ Successful: {count}", "failed": "❌ Failed: {count}", "manualConfirmationNote": "Some newsletters may require manual confirmation via email. Check your inbox for confirmation emails.", "nextSyncIn": "Next Sync In", "minutes": "minutes", "notAvailable": "N/A", "spamEmailSummary": "Spam email from {from}, subject: {subject}, date: {date}", "fromSummary": "From: {from}\n{date}", "label": "Label", "home": "Home", "@home": {"description": "Home screen title"}, "totalEmails": "Total Emails: {count}", "@totalEmails": {"description": "Label for total emails count on home screen", "placeholders": {"count": {"type": "int"}}}, "pleaseLogInFirst": "Please log in first", "@pleaseLogInFirst": {"description": "Message shown when user is not logged in"}, "show": "Show", "@show": {"description": "Label for pagination show count"}, "perPage": "per page", "@perPage": {"description": "Label for pagination per page"}, "previous": "Previous", "@previous": {"description": "Tooltip for previous page button"}, "next": "Next", "@next": {"description": "Tooltip for next page button"}, "dataModeSwitchTitle": "Switch Data Mode", "@dataModeSwitchTitle": {"description": "Title for the dialog when switching between online and offline modes"}, "guest": "Guest", "@guest": {"description": "Label for guest user when not signed in"}, "notSignedIn": "Not signed in", "@notSignedIn": {"description": "Subtitle for guest user when not signed in"}, "unknownUser": "Unknown user", "@unknownUser": {"description": "Label for unknown user if displayName is missing"}, "noCategoriesFound": "No categories found", "@noCategoriesFound": {"description": "Message when no Gmail categories are found"}, "autoLogout": "Auto Logout", "autoLogoutDescription": "Automatically log out after a period of inactivity", "autoLogoutNever": "Never", "autoLogout5min": "5 minutes", "autoLogout15min": "15 minutes", "autoLogout30min": "30 minutes", "autoLogout1hour": "1 hour", "autoLogout3hours": "3 hours", "autoLogout6hours": "6 hours", "autoLogout12hours": "12 hours", "autoLogout24hours": "24 hours", "autoLogout48hours": "48 hours", "autoRefresh": "Auto Refresh", "autoRefreshDescription": "Automatically refresh emails in online mode", "autoRefreshNever": "Never", "autoRefresh5min": "5 minutes", "autoRefresh15min": "15 minutes", "autoRefresh30min": "30 minutes", "autoRefresh1hour": "1 hour", "autoRefresh2hours": "2 hours", "autoRefresh6hours": "6 hours", "autoRefresh12hours": "12 hours", "autoRefresh24hours": "24 hours", "sentLabel": "<PERSON><PERSON>", "@sentLabel": {"description": "Sent folder label"}, "trashLabel": "Trash", "@trashLabel": {"description": "Trash folder label"}, "socialLabel": "Social", "@socialLabel": {"description": "Social category label"}, "notificationsLabel": "Notifications", "@notificationsLabel": {"description": "Notifications category label"}, "forumsLabel": "Forums", "@forumsLabel": {"description": "Forums category label"}, "promotionsLabel": "Promotions", "@promotionsLabel": {"description": "Promotions category label"}, "korean": "Korean", "@korean": {"description": "Korean language option"}, "switchToOnlineModePrompt": "Switching to online mode will fetch the latest emails from Gmail. Continue?", "@switchToOnlineModePrompt": {"description": "Prompt shown when switching to online mode"}, "switchToOfflineModePrompt": "Switching to offline mode will use cached emails only. Continue?", "@switchToOfflineModePrompt": {"description": "Prompt shown when switching to offline mode"}, "loadingProfile": "Loading profile...", "@loadingProfile": {"description": "Shown when loading user profile"}, "loadingEmails": "Loading emails...", "@loadingEmails": {"description": "Shown when loading emails"}, "online": "Online", "@online": {"description": "Label for online mode"}, "offline": "Offline", "@offline": {"description": "Label for offline mode"}, "composeEmailNotImplemented": "Compose email is not implemented yet.", "@composeEmailNotImplemented": {"description": "Shown when compose email is not implemented"}, "composeEmail": "Compose Email", "@composeEmail": {"description": "Tooltip for compose email button"}, "appDescription": "Simple and Secure Email", "@appDescription": {"description": "App tagline or description"}, "version": "Version", "@version": {"description": "Label for app version"}, "syncFetchingEmails": "Fetching emails...", "@syncFetchingEmails": {"description": "Message shown when fetching emails during sync"}, "syncProcessingEmails": "Processing emails...", "@syncProcessingEmails": {"description": "Message shown when processing emails during sync"}, "syncSyncingLabels": "Syncing labels...", "@syncSyncingLabels": {"description": "Message shown when syncing labels during sync"}, "syncCompleted": "Sync completed", "@syncCompleted": {"description": "Message shown when sync is completed"}, "spamStatistics": "Spam Statistics", "totalSpamEmails": "Total Spam Emails", "totalSpamSize": "Total Spam Size", "appName": "LavaMail", "emailCategories": "Email Categories", "refreshCounts": "Refresh counts", "emailInformation": "Email Information", "totalEmailsLabel": "Total emails", "attachments": "Attachments", "noAttachmentsDetected": "No attachments detected", "attachmentsDetected": "Yes ({count} attachments in {emails} emails)", "@attachmentsDetected": {"description": "Message when attachments are detected", "placeholders": {"count": {"type": "int", "description": "Number of attachments detected"}, "emails": {"type": "int", "description": "Number of emails with attachments"}}}, "error": "Error", "primary": "Primary", "social": "Social", "promotions": "Promotions", "updates": "Updates", "forums": "Forums", "primaryCategoryDetails": "Primary category details coming soon!", "socialCategoryDetails": "Social category details coming soon!", "promotionsCategoryDetails": "Promotions category details coming soon!", "updatesCategoryDetails": "Updates category details coming soon!", "forumsCategoryDetails": "Forums category details coming soon!", "accountDetails": "Account details coming soon!", "emailsLoadedSuccessfully": "Emails loaded successfully", "errorLoadingEmails": "Error loading emails", "emailSync": "Email sync", "initializing": "Initializing...", "account": "Account", "currentMode": "Current mode", "autoSync": "Auto Sync", "automaticallySyncEmails": "Automatically sync emails in background", "networkSettings": "Network Settings", "preferredNetworkType": "Preferred Network Type", "networkTypeAuto": "Auto", "networkTypeAutoDescription": "Automatically choose the best connection", "networkTypeWifi": "WiFi", "networkTypeMobile": "Mobile Data", "currentlyUsing": "Currently using", "refreshNetworkStatus": "Refresh network status", "enableNotifications": "Enable Notifications", "receiveNotifications": "Receive notifications for new emails", "security": "Security", "manualSyncTooltip": "Manual Sync", "syncing": "Syncing...", "signOut": "Sign Out", "synchronizationFailed": "Synchronization failed", "user": "User", "gmailUser": "Gmail User", "signInToGmail": "Sign in to Gmail", "signInWithEmailAppPassword": "Sign in with email and app password", "emailAddress": "Email Address", "enterValidEmail": "Please enter a valid email address", "appPasswordLabel": "16-digit App Password", "enterAppPassword16": "Please enter your 16-character app password", "gmailAppPasswordInfo": "Important: You must use a Gmail app password (not your main Google password).\nGenerate one in your Google account security settings.", "saveCredentialsCheckbox": "Save credentials for automatic login", "credentialsStoredSecurely": "Your credentials are stored securely on this device", "signInToGmx": "Sign in to GMX", "gmxLogin": "GMX Login", "savedCredentialsFound": "Saved credentials found", "connectWithSavedCredentials": "Connect with saved credentials", "useDifferentCredentials": "Use different credentials", "password": "Password", "gmxPasswordHint": "Your GMX password or app-specific password", "pleaseEnterYourPassword": "Please enter your password", "advancedSettings": "Advanced Settings", "imapHost": "IMAP Host", "smtpHost": "SMTP Host", "smtpConfiguration": "SMTP Configuration:", "port587Starttls": "Port 587 (STARTTLS)", "recommended": "Recommended", "port465SslTls": "Port 465 (SSL/TLS)", "required": "Required", "invalid": "Invalid", "loginToGmx": "Login to GMX", "importantSetupInformation": "Important Setup Information", "gmxEnableImapInstructions": "1. Enable IMAP/SMTP access in your GMX account:\n   • Log into GMX webmail\n   • Go to Settings → POP3/IMAP\n   • Enable IMAP access", "ifYouHave2faEnabled": "If you have 2FA enabled:", "gmxAppPasswordInstructions": "• Create an app-specific password in GMX Settings → Security\n• Use the app-specific password instead of your regular password", "gmxDefaultSettings": "Default settings:\n• IMAP: imap.gmx.com:993 (SSL)\n• SMTP: mail.gmx.com:587 (STARTTLS) or :465 (SSL/TLS)", "credentialsSavedSuccessfully": "Credentials saved successfully", "errorSavingCredentials": "Error saving credentials", "noSavedCredentialsFound": "No saved credentials found", "loginFailed": "<PERSON><PERSON> failed: {error}", "@loginFailed": {"description": "Error message when login fails", "placeholders": {"error": {"type": "String"}}}, "signInToIcloud": "Sign in to iCloud", "icloudLogin": "iCloud Login", "signInToYahoo": "Sign in to Yahoo", "yahooLogin": "Yahoo Login", "appPassword": "App Password", "icloudAppPasswordHint": "Your iCloud app password", "yahooAppPasswordHint": "Your Yahoo app password", "pleaseEnterYourAppPassword": "Please enter your app password", "icloudAppPasswordLength": "iCloud app passwords are typically 16 characters", "yahooAppPasswordLength": "Yahoo app passwords are typically 16 characters", "showAdvancedSettings": "Show Advanced Settings", "hideAdvancedSettings": "Hide Advanced Settings", "imapPort": "IMAP Port", "smtpPort": "SMTP Port", "useSSL": "Use SSL", "loginToIcloud": "Login to iCloud", "loginToYahoo": "Login to Yahoo", "icloudImportantInfo": "Important: You must use an iCloud app password (not your main Apple ID password).\nGenerate one in your Apple ID security settings.\n\nDefault settings:\n• IMAP: imap.mail.me.com:993 (SSL)\n• SMTP: smtp.mail.me.com:587 (SSL)", "yahooImportantInfo": "Important: You must use a Yahoo app password (not your main Yahoo password).\nGenerate one in your Yahoo account security settings.\n\nDefault settings:\n• IMAP: imap.mail.yahoo.com:993 (SSL)\n• SMTP: smtp.mail.yahoo.com:465 (SSL)", "port": "Port", "quickCleanButton": "Quick Clean {label}", "@quickCleanButton": {"description": "Button to quickly delete all emails in a category.", "placeholders": {"label": {"type": "String"}}}, "quickCleanConfirmTitle": "Confirm Quick Clean", "@quickCleanConfirmTitle": {"description": "Title of the confirmation dialog for quick clean."}, "quickCleanConfirmContent": "Do you really want to delete all emails in the {label} category? This action cannot be undone.", "@quickCleanConfirmContent": {"description": "Content of the confirmation dialog for quick clean.", "placeholders": {"label": {"type": "String"}}}, "quickCleanSuccess": "{count} emails deleted successfully!", "@quickCleanSuccess": {"description": "Success message after quick clean.", "placeholders": {"count": {"type": "int"}}}}